<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>كتاب السبع صلوات - برنامج الكنيسة</title>
    <link href="https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            min-height: 100vh;
            color: #333;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            min-height: 100vh;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            border: 3px solid #1e3c72;
            border-radius: 20px;
            padding: 30px;
            text-align: center;
            margin-bottom: 30px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
        }

        .header h1 {
            font-family: '<PERSON>i', serif;
            font-size: 2.5rem;
            color: #1e3c72;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
        }

        .header p {
            font-size: 1.2rem;
            color: #666;
            margin-bottom: 20px;
        }

        .back-btn {
            background: linear-gradient(45deg, #e74c3c, #c0392b);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            font-family: 'Cairo', sans-serif;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .back-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(231, 76, 60, 0.4);
        }

        .controls {
            background: rgba(255, 255, 255, 0.95);
            border: 3px solid #1e3c72;
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
        }

        .dropdown-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 20px;
        }

        .dropdown-group {
            display: flex;
            flex-direction: column;
        }

        .dropdown-label {
            font-family: 'Amiri', serif;
            font-size: 1.3rem;
            font-weight: 700;
            color: #1e3c72;
            margin-bottom: 10px;
            text-align: center;
        }

        .dropdown {
            padding: 15px;
            border: 3px solid #1e3c72;
            border-radius: 15px;
            font-family: 'Cairo', sans-serif;
            font-size: 1.1rem;
            background: white;
            color: #333;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .dropdown:hover {
            border-color: #ffd700;
            box-shadow: 0 5px 15px rgba(255, 215, 0, 0.3);
        }

        .dropdown:focus {
            outline: none;
            border-color: #ffd700;
            box-shadow: 0 0 0 3px rgba(255, 215, 0, 0.2);
        }

        .content-area {
            background: rgba(255, 255, 255, 0.98);
            border: 4px solid #000;
            border-radius: 20px;
            padding: 40px;
            min-height: 600px;
            box-shadow: 0 20px 50px rgba(0, 0, 0, 0.3);
            font-family: 'Amiri', serif;
            line-height: 2;
        }

        .prayer-title {
            font-size: 2rem;
            color: #1e3c72;
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(45deg, #ffd700, #ffed4e);
            border-radius: 15px;
            border: 3px solid #1e3c72;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
        }

        .prayer-text {
            font-size: 1.4rem;
            color: #2c3e50;
            text-align: justify;
            margin-bottom: 25px;
            padding: 20px;
            background: rgba(240, 248, 255, 0.8);
            border-radius: 10px;
            border-right: 5px solid #1e3c72;
        }

        .verse-number {
            color: #e74c3c;
            font-weight: bold;
            font-size: 1.2em;
        }

        .section-divider {
            text-align: center;
            margin: 30px 0;
            color: #1e3c72;
            font-size: 1.5rem;
            font-weight: bold;
        }

        .loading-message {
            text-align: center;
            font-size: 1.5rem;
            color: #666;
            padding: 50px;
        }

        @media (max-width: 768px) {
            .dropdown-container {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .content-area {
                padding: 20px;
            }
            
            .prayer-text {
                font-size: 1.2rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📿 كتاب السبع صلوات 📿</h1>
            <p>الصلوات السبع المقدسة للكنيسة القبطية الأرثوذكسية</p>
            <a href="main-app.html" class="back-btn">← العودة للصفحة الرئيسية</a>
        </div>

        <div class="controls">
            <div class="dropdown-container">
                <div class="dropdown-group">
                    <label class="dropdown-label">اختر الصلاة</label>
                    <select id="prayerSelect" class="dropdown">
                        <option value="">-- اختر الصلاة --</option>
                        <option value="matins">صلاة باكر</option>
                        <option value="third">صلاة الساعة الثالثة</option>
                        <option value="sixth">صلاة الساعة السادسة</option>
                        <option value="ninth">صلاة الساعة التاسعة</option>
                        <option value="vespers">صلاة الغروب</option>
                        <option value="compline">صلاة النوم</option>
                        <option value="midnight">صلاة نصف الليل</option>
                    </select>
                </div>

                <div class="dropdown-group">
                    <label class="dropdown-label">اختر الجزء</label>
                    <select id="sectionSelect" class="dropdown" disabled>
                        <option value="">-- اختر الجزء --</option>
                    </select>
                </div>
            </div>
        </div>

        <div class="content-area" id="contentArea">
            <div class="loading-message">
                🙏 اختر الصلاة والجزء المطلوب لعرض النص 🙏
            </div>
        </div>
    </div>

    <script src="seven-prayers.js"></script>
</body>
</html>
