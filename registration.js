// نظام التسجيل متعدد الخطوات لبرنامج الكنيسة

// دالة تحويل التاريخ الهجري إلى الميلادي
function convertHijriToGregorian(hijriYear, hijriMonth, hijriDay) {
    try {
        // معادلة تقريبية لتحويل التاريخ الهجري إلى الميلادي
        const hijriEpoch = 227015; // بداية التقويم الهجري بالأيام اليوليانية
        const gregorianEpoch = 1948439.5; // بداية التقويم الميلادي بالأيام اليوليانية

        // حساب عدد الأيام من بداية التقويم الهجري
        const totalDays = (hijriYear - 1) * 354.367 + (hijriMonth - 1) * 29.53 + hijriDay;

        // تحويل إلى التاريخ الميلادي
        const julianDay = hijriEpoch + totalDays;
        const gregorianDay = julianDay - gregorianEpoch;

        // تحويل إلى كائن التاريخ
        const baseDate = new Date(1970, 0, 1); // 1 يناير 1970
        const resultDate = new Date(baseDate.getTime() + gregorianDay * 24 * 60 * 60 * 1000);

        // تصحيح تقريبي
        const correctionYears = Math.floor((hijriYear - 1400) / 33) * 1;
        resultDate.setFullYear(resultDate.getFullYear() - correctionYears);

        return resultDate;
    } catch (error) {
        return null;
    }
}

// دالة تحديث التاريخ الميلادي
function updateGregorianDate() {
    const day = document.getElementById('birthDay').value;
    const month = document.getElementById('birthMonth').value;
    const year = document.getElementById('birthYear').value;
    const gregorianDateDiv = document.getElementById('gregorianDate');

    if (day && month && year) {
        const gregorianDate = convertHijriToGregorian(parseInt(year), parseInt(month), parseInt(day));
        if (gregorianDate) {
            const options = { year: 'numeric', month: 'long', day: 'numeric' };
            gregorianDateDiv.textContent = gregorianDate.toLocaleDateString('ar-EG', options);
            gregorianDateDiv.style.color = '#27ae60';
        } else {
            gregorianDateDiv.textContent = 'تاريخ غير صحيح';
            gregorianDateDiv.style.color = '#e74c3c';
        }
    } else {
        gregorianDateDiv.textContent = 'سيظهر بعد إدخال التاريخ الهجري';
        gregorianDateDiv.style.color = '#666';
    }
}

document.addEventListener('DOMContentLoaded', function() {
    let currentStep = 1;
    const totalSteps = 5;
    const userData = {};

    // عناصر الصفحة
    const stepIndicator = document.getElementById('currentStep');
    const progressFill = document.getElementById('progressFill');
    const nextBtn = document.getElementById('nextBtn');
    const form = document.getElementById('registrationForm');

    // تحديث شريط التقدم
    function updateProgress() {
        const progress = (currentStep / totalSteps) * 100;
        progressFill.style.width = progress + '%';
        stepIndicator.textContent = currentStep;
    }

    // إظهار الخطوة الحالية
    function showStep(step) {
        // إخفاء جميع الخطوات
        document.querySelectorAll('.form-step').forEach(stepEl => {
            stepEl.style.display = 'none';
        });
        
        // إظهار الخطوة الحالية
        document.getElementById('step' + step).style.display = 'block';
        
        // تحديث نص الزر
        if (step === totalSteps) {
            nextBtn.textContent = 'إنهاء التسجيل';
            nextBtn.style.background = 'linear-gradient(45deg, #27ae60, #2ecc71)';
        } else {
            nextBtn.textContent = 'التالي';
            nextBtn.style.background = 'linear-gradient(45deg, #1e3c72, #2a5298)';
        }
        
        updateProgress();
    }

    // التحقق من صحة البيانات
    function validateStep(step) {
        let isValid = true;
        
        switch(step) {
            case 1:
                const name = document.getElementById('fullName').value.trim();
                const nameError = document.getElementById('nameError');
                
                if (name.length < 2 || !/^[\u0600-\u06FF\s]+$/.test(name)) {
                    nameError.style.display = 'block';
                    isValid = false;
                } else {
                    nameError.style.display = 'none';
                    userData.name = name;
                }
                break;
                
            case 2:
                const birthDay = document.getElementById('birthDay').value;
                const birthMonth = document.getElementById('birthMonth').value;
                const birthYear = document.getElementById('birthYear').value;
                const dateError = document.getElementById('dateError');

                if (!birthDay || !birthMonth || !birthYear) {
                    dateError.textContent = 'يرجى إدخال تاريخ الميلاد كاملاً';
                    dateError.style.display = 'block';
                    isValid = false;
                } else {
                    const hijriDate = `${birthYear}-${birthMonth.padStart(2, '0')}-${birthDay.padStart(2, '0')}`;
                    const gregorianDate = convertHijriToGregorian(parseInt(birthYear), parseInt(birthMonth), parseInt(birthDay));

                    if (!gregorianDate) {
                        dateError.textContent = 'تاريخ غير صحيح';
                        dateError.style.display = 'block';
                        isValid = false;
                    } else {
                        const today = new Date();
                        const age = today.getFullYear() - gregorianDate.getFullYear();

                        if (age < 5 || age > 120) {
                            dateError.textContent = 'العمر يجب أن يكون بين 5 و 120 سنة';
                            dateError.style.display = 'block';
                            isValid = false;
                        } else {
                            dateError.style.display = 'none';
                            userData.birthDateHijri = hijriDate;
                            userData.birthDateGregorian = gregorianDate.toISOString().split('T')[0];
                            userData.age = age;
                        }
                    }
                }
                break;
                
            case 3:
                const gender = document.querySelector('input[name="gender"]:checked');
                const genderError = document.getElementById('genderError');
                
                if (!gender) {
                    genderError.style.display = 'block';
                    isValid = false;
                } else {
                    genderError.style.display = 'none';
                    userData.gender = gender.value;
                }
                break;
                
            case 4:
                // الصورة اختيارية
                const photo = document.getElementById('profilePhoto').files[0];
                if (photo) {
                    userData.hasPhoto = true;
                    document.getElementById('photoSuccess').style.display = 'block';
                } else {
                    userData.hasPhoto = false;
                }
                break;
                
            case 5:
                const phone = document.getElementById('phoneNumber').value.trim();
                const phoneError = document.getElementById('phoneError');
                
                if (!/^01[0-9]{9}$/.test(phone)) {
                    phoneError.style.display = 'block';
                    isValid = false;
                } else {
                    phoneError.style.display = 'none';
                    userData.phone = phone;
                }
                break;
        }
        
        return isValid;
    }

    // حفظ البيانات
    function saveUserData() {
        localStorage.setItem('church_user_data', JSON.stringify(userData));
        localStorage.setItem('church_registration_complete', 'true');
        localStorage.setItem('church_registration_date', new Date().toISOString());
    }

    // تدليع الاسم
    function getNickname(fullName) {
        const names = fullName.trim().split(' ');
        const firstName = names[0];
        
        // قائمة بأسماء مدللة شائعة
        const nicknames = {
            'محمد': 'حمودي',
            'أحمد': 'حمودة',
            'علي': 'علوش',
            'حسن': 'حسونة',
            'محمود': 'حمودي',
            'عبدالله': 'عبدو',
            'يوسف': 'يويو',
            'عمر': 'عمورة',
            'مريم': 'ميمي',
            'فاطمة': 'فوفو',
            'عائشة': 'عوشة',
            'خديجة': 'خوخة',
            'زينب': 'زوزو',
            'سارة': 'سوسو',
            'نور': 'نونو',
            'ياسمين': 'يويو',
            'مينا': 'مينو',
            'جورج': 'جوجو',
            'مارك': 'ماركو',
            'بولس': 'بولو',
            'بطرس': 'بيتر',
            'يوحنا': 'جونو',
            'مريم': 'ميري',
            'مارتا': 'مارتو',
            'إليزابيث': 'ليزي',
            'كريستين': 'كريس'
        };
        
        return nicknames[firstName] || firstName;
    }

    // معالج النقر على الزر
    nextBtn.addEventListener('click', function() {
        if (validateStep(currentStep)) {
            if (currentStep < totalSteps) {
                currentStep++;
                showStep(currentStep);
            } else {
                // إنهاء التسجيل
                saveUserData();
                
                // تأثير التحميل
                nextBtn.textContent = 'جاري الحفظ...';
                nextBtn.disabled = true;
                
                setTimeout(() => {
                    window.location.href = 'welcome.html';
                }, 1500);
            }
        }
    });

    // معالج اختيار النوع
    document.querySelectorAll('input[name="gender"]').forEach(radio => {
        radio.addEventListener('change', function() {
            document.querySelectorAll('.gender-option').forEach(option => {
                option.classList.remove('selected');
            });
            this.parentElement.classList.add('selected');
        });
    });

    // معالج رفع الصورة
    document.getElementById('profilePhoto').addEventListener('change', function() {
        if (this.files[0]) {
            const reader = new FileReader();
            reader.onload = function(e) {
                userData.photoData = e.target.result;
            };
            reader.readAsDataURL(this.files[0]);
        }
    });

    // معالجات تحديث التاريخ الميلادي
    document.getElementById('birthDay').addEventListener('input', updateGregorianDate);
    document.getElementById('birthMonth').addEventListener('change', updateGregorianDate);
    document.getElementById('birthYear').addEventListener('input', updateGregorianDate);

    // تحسين تجربة المستخدم
    document.querySelectorAll('.form-input').forEach(input => {
        input.addEventListener('input', function() {
            // إخفاء رسائل الخطأ عند الكتابة
            const errorMsg = this.parentElement.querySelector('.error-message');
            if (errorMsg) {
                errorMsg.style.display = 'none';
            }
        });
        
        // تأثير التركيز
        input.addEventListener('focus', function() {
            this.parentElement.style.transform = 'scale(1.02)';
            this.parentElement.style.transition = 'transform 0.2s ease';
        });
        
        input.addEventListener('blur', function() {
            this.parentElement.style.transform = 'scale(1)';
        });
    });

    // دعم لوحة المفاتيح
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Enter') {
            e.preventDefault();
            nextBtn.click();
        }
    });

    // التحقق من الموافقة على الشروط
    const termsAgreed = localStorage.getItem('church_app_terms_agreed');
    if (termsAgreed !== 'true') {
        alert('يجب الموافقة على الشروط والأحكام أولاً');
        window.location.href = 'terms-and-conditions.html';
        return;
    }

    // بدء العرض
    showStep(1);
    
    // رسالة ترحيب
    setTimeout(() => {
        console.log('مرحباً بك في نظام التسجيل لبرنامج الكنيسة الأجبية والصلوات');
    }, 500);
});
