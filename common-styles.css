/* ملف CSS مشترك لتحسين الأداء والاستجابة السريعة */

/* تحسين الأداء العام */
* {
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
}

body {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
    overflow-x: hidden;
}

/* تحسين الخطوط */
@font-display: swap;

/* تحسين الصور */
img {
    max-width: 100%;
    height: auto;
    display: block;
}

/* تحسين الأزرار */
button, .btn {
    cursor: pointer;
    border: none;
    outline: none;
    -webkit-tap-highlight-color: transparent;
    user-select: none;
    touch-action: manipulation;
}

button:focus, .btn:focus {
    outline: 2px solid #ffd700;
    outline-offset: 2px;
}

/* تحسين النماذج */
input, textarea, select {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    border-radius: 0;
}

input:focus, textarea:focus, select:focus {
    outline: 2px solid #ffd700;
    outline-offset: 2px;
}

/* تحسين الروابط */
a {
    color: inherit;
    text-decoration: none;
    -webkit-tap-highlight-color: transparent;
}

a:focus {
    outline: 2px solid #ffd700;
    outline-offset: 2px;
}

/* تحسين الاستجابة للشاشات الصغيرة */
@media (max-width: 768px) {
    body {
        font-size: 16px; /* منع التكبير التلقائي في iOS */
    }
    
    input, textarea, select {
        font-size: 16px; /* منع التكبير التلقائي في iOS */
    }
    
    button, .btn {
        min-height: 44px; /* الحد الأدنى لحجم اللمس */
        min-width: 44px;
    }
}

/* تحسين الأداء للعناصر المتحركة */
.smooth-transition {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.fast-transition {
    transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
}

/* تحسين التمرير */
.scroll-smooth {
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
}

/* إخفاء شريط التمرير مع الحفاظ على الوظيفة */
.hide-scrollbar {
    -ms-overflow-style: none;
    scrollbar-width: none;
}

.hide-scrollbar::-webkit-scrollbar {
    display: none;
}

/* تحسين الظلال للأداء */
.optimized-shadow {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    will-change: box-shadow;
}

.optimized-shadow:hover {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* تحسين التدرجات */
.optimized-gradient {
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 50%, #87ceeb 100%);
    background-attachment: fixed;
    background-size: 100% 100%;
}

/* تحسين الشفافية */
.optimized-backdrop {
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

/* تحسين الرسوم المتحركة */
@media (prefers-reduced-motion: reduce) {
    *, *::before, *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* تحسين الطباعة */
@media print {
    * {
        background: transparent !important;
        color: black !important;
        box-shadow: none !important;
        text-shadow: none !important;
    }
    
    .no-print {
        display: none !important;
    }
}

/* تحسين الوضع المظلم */
@media (prefers-color-scheme: dark) {
    .auto-dark {
        filter: invert(1) hue-rotate(180deg);
    }
    
    .auto-dark img, .auto-dark video {
        filter: invert(1) hue-rotate(180deg);
    }
}

/* تحسين الشاشات عالية الدقة */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .high-dpi {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }
}

/* تحسين اللمس */
.touch-friendly {
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

.touch-friendly:active {
    transform: scale(0.98);
}

/* تحسين التحميل */
.loading-skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

/* تحسين الشبكة */
.grid-responsive {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
}

/* تحسين الفليكس */
.flex-responsive {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
}

.flex-responsive > * {
    flex: 1 1 250px;
}

/* تحسين النصوص */
.text-responsive {
    font-size: clamp(1rem, 2.5vw, 1.5rem);
    line-height: 1.6;
}

/* تحسين المسافات */
.spacing-responsive {
    padding: clamp(1rem, 5vw, 3rem);
    margin: clamp(0.5rem, 2.5vw, 1.5rem) 0;
}

/* تحسين العرض */
.container-responsive {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 clamp(1rem, 5vw, 2rem);
}

/* تحسين الأيقونات */
.icon-responsive {
    width: clamp(1.5rem, 4vw, 2.5rem);
    height: clamp(1.5rem, 4vw, 2.5rem);
    display: inline-block;
    vertical-align: middle;
}

/* تحسين الجداول */
.table-responsive {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
}

.table-responsive table {
    min-width: 600px;
    width: 100%;
}

/* تحسين الكروت */
.card-responsive {
    background: white;
    border-radius: clamp(0.5rem, 2vw, 1rem);
    padding: clamp(1rem, 4vw, 2rem);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* تحسين الأزرار المتجاوبة */
.btn-responsive {
    padding: clamp(0.75rem, 3vw, 1rem) clamp(1.5rem, 6vw, 2rem);
    font-size: clamp(0.875rem, 2.5vw, 1rem);
    border-radius: clamp(0.5rem, 2vw, 1rem);
    min-height: 44px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

/* تحسين النماذج المتجاوبة */
.form-responsive {
    display: grid;
    gap: clamp(1rem, 3vw, 1.5rem);
}

.form-responsive input,
.form-responsive textarea,
.form-responsive select {
    padding: clamp(0.75rem, 3vw, 1rem);
    font-size: clamp(0.875rem, 2.5vw, 1rem);
    border-radius: clamp(0.5rem, 2vw, 1rem);
    min-height: 44px;
}

/* تحسين الشعارات */
.logo-responsive {
    width: clamp(60px, 15vw, 120px);
    height: clamp(60px, 15vw, 120px);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* تحسين العناوين */
.title-responsive {
    font-size: clamp(1.5rem, 5vw, 3rem);
    line-height: 1.2;
    margin-bottom: clamp(1rem, 3vw, 2rem);
}

.subtitle-responsive {
    font-size: clamp(1rem, 3vw, 1.5rem);
    line-height: 1.4;
    margin-bottom: clamp(0.5rem, 2vw, 1rem);
}
