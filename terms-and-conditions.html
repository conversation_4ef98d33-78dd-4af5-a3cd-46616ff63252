<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>شروط وأحكام - برنامج الكنيسة الأجبية والصلوات</title>
    <link href="https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="common-styles.css">
    <link rel="stylesheet" href="styles.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', '<PERSON><PERSON>', serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 50%, #87ceeb 100%);
            min-height: 100vh;
            color: #333;
            line-height: 1.8;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 30px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            border: 2px solid rgba(255, 215, 0, 0.3);
        }

        .logo {
            width: 120px;
            height: 120px;
            margin: 0 auto 20px;
            background: linear-gradient(45deg, #ffd700, #ffed4e);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 10px 30px rgba(255, 215, 0, 0.4);
            position: relative;
            overflow: hidden;
        }

        .logo::before {
            content: '✞';
            font-size: 60px;
            color: #1e3c72;
            font-weight: bold;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .logo::after {
            content: '';
            position: absolute;
            top: -20%;
            left: -20%;
            width: 140%;
            height: 140%;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
        }

        .app-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: #1e3c72;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
            font-family: 'Amiri', serif;
        }

        .app-subtitle {
            font-size: 1.2rem;
            color: #666;
            margin-bottom: 20px;
            font-weight: 300;
        }

        .verse {
            background: linear-gradient(45deg, #ffd700, #ffed4e);
            padding: 20px;
            border-radius: 15px;
            font-style: italic;
            font-size: 1.1rem;
            color: #1e3c72;
            border-right: 5px solid #1e3c72;
            margin-top: 20px;
            box-shadow: 0 5px 15px rgba(255, 215, 0, 0.3);
        }

        .content {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            border: 2px solid rgba(255, 215, 0, 0.2);
        }

        .section-title {
            font-size: 1.8rem;
            font-weight: 700;
            color: #1e3c72;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 3px solid #ffd700;
            position: relative;
        }

        .section-title::before {
            content: '✞';
            position: absolute;
            right: -30px;
            top: 0;
            color: #ffd700;
            font-size: 1.2rem;
        }

        .terms-list {
            list-style: none;
            padding: 0;
        }

        .terms-list li {
            background: rgba(30, 60, 114, 0.05);
            margin-bottom: 15px;
            padding: 20px;
            border-radius: 12px;
            border-right: 4px solid #ffd700;
            transition: all 0.3s ease;
            position: relative;
        }

        .terms-list li:hover {
            transform: translateX(-5px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            background: rgba(30, 60, 114, 0.08);
        }

        .terms-list li::before {
            content: counter(term-counter);
            counter-increment: term-counter;
            position: absolute;
            right: -15px;
            top: 15px;
            background: #ffd700;
            color: #1e3c72;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 0.9rem;
        }

        .terms-list {
            counter-reset: term-counter;
        }

        .highlight {
            background: linear-gradient(120deg, #ffd700 0%, #ffed4e 100%);
            padding: 2px 8px;
            border-radius: 5px;
            color: #1e3c72;
            font-weight: 600;
        }

        .footer {
            text-align: center;
            margin-top: 40px;
            padding: 30px;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 15px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }

        .contact-info {
            display: flex;
            justify-content: space-around;
            flex-wrap: wrap;
            margin-top: 20px;
        }

        .contact-item {
            background: linear-gradient(45deg, #1e3c72, #2a5298);
            color: white;
            padding: 15px 25px;
            border-radius: 25px;
            margin: 10px;
            box-shadow: 0 5px 15px rgba(30, 60, 114, 0.3);
            transition: transform 0.3s ease;
        }

        .contact-item:hover {
            transform: translateY(-3px);
        }

        @media (max-width: 768px) {
            .app-title {
                font-size: 2rem;
            }
            
            .container {
                padding: 10px;
            }
            
            .header, .content {
                padding: 20px;
            }
            
            .contact-info {
                flex-direction: column;
                align-items: center;
            }
        }

        .decorative-border {
            height: 5px;
            background: linear-gradient(90deg, #ffd700, #1e3c72, #ffd700);
            border-radius: 3px;
            margin: 30px 0;
        }
    </style>
</head>
<body>

    <div class="container">
        <div class="header">
            <div class="logo"></div>
            <h1 class="app-title">برنامج الكنيسة الأجبية والصلوات</h1>
            <p class="app-subtitle">تطبيق شامل للصلوات والتسابيح المسيحية</p>
            <div class="verse">
                "اطلبوا تجدوا، اقرعوا يُفتح لكم" - متى 7:7
            </div>
        </div>

        <div class="content">
            <h2 class="section-title">شروط وأحكام الاستخدام</h2>
            
            <div class="decorative-border"></div>
            
            <ul class="terms-list">
                <li>
                    <strong>قبول الشروط:</strong> باستخدام تطبيق <span class="highlight">برنامج الكنيسة الأجبية والصلوات</span>، فإنك توافق على جميع الشروط والأحكام المذكورة في هذه الصفحة.
                </li>
                
                <li>
                    <strong>الغرض من التطبيق:</strong> هذا التطبيق مخصص لخدمة المؤمنين المسيحيين وتوفير <span class="highlight">الصلوات والتسابيح والأجبية</span> بطريقة سهلة ومنظمة.
                </li>
                
                <li>
                    <strong>المحتوى الديني:</strong> جميع النصوص والصلوات المتوفرة في التطبيق مأخوذة من <span class="highlight">المصادر الكنسية المعتمدة</span> والتراث المسيحي الأصيل.
                </li>
                
                <li>
                    <strong>الاستخدام الشخصي:</strong> التطبيق مخصص للاستخدام الشخصي والروحي فقط، ولا يجوز استخدامه لأغراض تجارية دون إذن مسبق.
                </li>
                
                <li>
                    <strong>حقوق الملكية الفكرية:</strong> جميع حقوق الملكية الفكرية للتطبيق محفوظة، ولا يجوز نسخ أو توزيع المحتوى دون إذن.
                </li>

                <li>
                    <strong>الخصوصية وحماية البيانات:</strong> نحن نحترم خصوصيتك ولا نجمع أي بيانات شخصية دون موافقتك الصريحة. جميع المعلومات محمية وفقاً لأعلى معايير الأمان.
                </li>

                <li>
                    <strong>التحديثات والتطوير:</strong> قد يتم تحديث التطبيق دورياً لإضافة <span class="highlight">صلوات جديدة وتحسينات</span> على الأداء والتصميم.
                </li>

                <li>
                    <strong>المسؤولية:</strong> التطبيق يُقدم "كما هو" ولا نتحمل مسؤولية أي أضرار قد تنتج عن الاستخدام غير الصحيح.
                </li>

                <li>
                    <strong>الدعم الفني:</strong> نوفر الدعم الفني للمستخدمين لحل أي مشاكل تقنية قد تواجههم أثناء استخدام التطبيق.
                </li>

                <li>
                    <strong>التوافق مع الأجهزة:</strong> التطبيق متوافق مع جميع الأجهزة الذكية والمتصفحات الحديثة لضمان أفضل تجربة استخدام.
                </li>

                <li>
                    <strong>اللغة والمحتوى:</strong> التطبيق متوفر باللغة العربية مع إمكانية إضافة لغات أخرى في المستقبل حسب احتياجات المستخدمين.
                </li>

                <li>
                    <strong>الاستخدام المسؤول:</strong> يُرجى استخدام التطبيق بطريقة تتماشى مع <span class="highlight">القيم المسيحية والأخلاق الدينية</span>.
                </li>

                <li>
                    <strong>التواصل والاقتراحات:</strong> نرحب بجميع الاقتراحات والملاحظات لتطوير التطبيق وجعله أكثر فائدة للمجتمع المسيحي.
                </li>

                <li>
                    <strong>تعديل الشروط:</strong> نحتفظ بالحق في تعديل هذه الشروط والأحكام في أي وقت، وسيتم إشعار المستخدمين بأي تغييرات مهمة.
                </li>
            </ul>

            <div class="decorative-border"></div>

            <div style="background: linear-gradient(45deg, rgba(30, 60, 114, 0.1), rgba(255, 215, 0, 0.1)); padding: 30px; border-radius: 15px; margin-top: 30px; text-align: center;">
                <h3 style="color: #1e3c72; margin-bottom: 15px; font-size: 1.5rem;">رسالة روحية</h3>
                <p style="font-size: 1.1rem; color: #555; font-style: italic;">
                    "لأنه حيثما اجتمع اثنان أو ثلاثة باسمي فهناك أكون في وسطهم" - متى 18:20
                </p>
                <p style="margin-top: 15px; color: #666;">
                    نسأل الله أن يبارك استخدامكم لهذا التطبيق وأن يكون وسيلة للنمو الروحي والتقرب إلى الله
                </p>
            </div>
        </div>

        <div class="footer">
            <h3 style="color: #1e3c72; margin-bottom: 20px;">للتواصل معنا</h3>
            <div class="contact-info">
                <div class="contact-item">
                    📧 البريد الإلكتروني: <EMAIL>
                </div>
                <div class="contact-item">
                    📱 الهاتف: +20 123 456 7890
                </div>
                <div class="contact-item">
                    🌐 الموقع الإلكتروني: www.church-app.com
                </div>
            </div>

            <!-- زر الموافقة -->
            <div style="text-align: center; margin: 40px 0;">
                <button id="agreeBtn" style="
                    background: linear-gradient(45deg, #1e3c72, #2a5298);
                    color: white;
                    border: none;
                    padding: 20px 50px;
                    font-size: 1.3rem;
                    font-weight: 600;
                    border-radius: 50px;
                    cursor: pointer;
                    box-shadow: 0 8px 25px rgba(30, 60, 114, 0.3);
                    transition: all 0.3s ease;
                    font-family: 'Cairo', sans-serif;
                    min-width: 300px;
                ">
                    ✓ أوافق على جميع سياسات الخصوصية
                </button>
            </div>

            <div style="margin-top: 30px; padding-top: 20px; border-top: 2px solid #ffd700;">
                <p style="color: #666; font-size: 0.9rem;">
                    © 2024 برنامج الكنيسة الأجبية والصلوات - جميع الحقوق محفوظة
                </p>
                <p style="color: #888; font-size: 0.8rem; margin-top: 10px;">
                    تم التطوير بحب وإخلاص لخدمة المجتمع المسيحي
                </p>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
