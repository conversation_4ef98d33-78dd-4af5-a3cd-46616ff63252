<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>السنكسار اليومي - سيرة القديسين</title>
    <link href="https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #8B4513 0%, #D2691E 50%, #CD853F 100%);
            min-height: 100vh;
            color: #333;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            min-height: 100vh;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            border: 4px solid #8B4513;
            border-radius: 25px;
            padding: 40px;
            text-align: center;
            margin-bottom: 30px;
            box-shadow: 0 20px 50px rgba(0, 0, 0, 0.3);
        }

        .header h1 {
            font-family: 'Amiri', serif;
            font-size: 3rem;
            color: #8B4513;
            margin-bottom: 15px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
        }

        .header p {
            font-size: 1.3rem;
            color: #666;
            margin-bottom: 25px;
        }

        .back-btn {
            background: linear-gradient(45deg, #e74c3c, #c0392b);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 30px;
            font-family: 'Cairo', sans-serif;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            font-size: 1.1rem;
        }

        .back-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(231, 76, 60, 0.4);
        }

        .date-selector {
            background: rgba(255, 255, 255, 0.95);
            border: 4px solid #8B4513;
            border-radius: 25px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
            text-align: center;
        }

        .date-selector h2 {
            font-family: 'Amiri', serif;
            font-size: 2rem;
            color: #8B4513;
            margin-bottom: 20px;
        }

        .date-input {
            padding: 15px 20px;
            border: 3px solid #8B4513;
            border-radius: 15px;
            font-family: 'Cairo', sans-serif;
            font-size: 1.2rem;
            background: white;
            color: #333;
            margin: 0 10px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .date-input:hover {
            border-color: #D2691E;
            box-shadow: 0 5px 15px rgba(139, 69, 19, 0.3);
        }

        .date-input:focus {
            outline: none;
            border-color: #D2691E;
            box-shadow: 0 0 0 3px rgba(139, 69, 19, 0.2);
        }

        .load-btn {
            background: linear-gradient(45deg, #8B4513, #D2691E);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-family: 'Cairo', sans-serif;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 1.1rem;
            margin: 20px 10px;
        }

        .load-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(139, 69, 19, 0.4);
        }

        .today-btn {
            background: linear-gradient(45deg, #27ae60, #2ecc71);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-family: 'Cairo', sans-serif;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 1.1rem;
            margin: 20px 10px;
        }

        .today-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(39, 174, 96, 0.4);
        }

        .content-area {
            background: rgba(255, 255, 255, 0.98);
            border: 5px solid #8B4513;
            border-radius: 25px;
            padding: 50px;
            min-height: 600px;
            box-shadow: 0 25px 60px rgba(0, 0, 0, 0.3);
            font-family: 'Amiri', serif;
            line-height: 2.2;
        }

        .synaxarium-title {
            font-size: 2.5rem;
            color: #8B4513;
            text-align: center;
            margin-bottom: 40px;
            padding: 25px;
            background: linear-gradient(45deg, #FFD700, #FFA500);
            border-radius: 20px;
            border: 4px solid #8B4513;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
        }

        .synaxarium-content {
            font-size: 1.6rem;
            color: #2c3e50;
            text-align: justify;
            margin-bottom: 30px;
            padding: 30px;
            background: rgba(255, 248, 220, 0.8);
            border-radius: 15px;
            border-right: 6px solid #8B4513;
        }

        .saint-name {
            color: #8B4513;
            font-weight: bold;
            font-size: 1.2em;
            text-decoration: underline;
        }

        .loading-message {
            text-align: center;
            font-size: 1.8rem;
            color: #8B4513;
            padding: 80px;
            background: rgba(255, 248, 220, 0.5);
            border-radius: 20px;
            border: 3px dashed #D2691E;
        }

        .error-message {
            text-align: center;
            font-size: 1.5rem;
            color: #e74c3c;
            padding: 50px;
            background: rgba(255, 235, 235, 0.8);
            border-radius: 15px;
            border: 3px solid #e74c3c;
        }

        .source-link {
            text-align: center;
            margin-top: 30px;
            padding: 20px;
            background: rgba(139, 69, 19, 0.1);
            border-radius: 15px;
            border: 2px solid #8B4513;
        }

        .source-link a {
            color: #8B4513;
            text-decoration: none;
            font-weight: bold;
            font-size: 1.1rem;
        }

        .source-link a:hover {
            color: #D2691E;
            text-decoration: underline;
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2.2rem;
            }
            
            .content-area {
                padding: 25px;
            }
            
            .synaxarium-content {
                font-size: 1.3rem;
                padding: 20px;
            }
            
            .date-input, .load-btn, .today-btn {
                margin: 10px 5px;
                padding: 12px 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>👑 السنكسار اليومي 👑</h1>
            <p>سيرة القديسين والشهداء والآباء الأطهار</p>
            <a href="main-app.html" class="back-btn">← العودة للصفحة الرئيسية</a>
        </div>

        <div class="date-selector">
            <h2>اختر التاريخ</h2>
            <input type="date" id="dateInput" class="date-input">
            <br>
            <button id="loadBtn" class="load-btn">📖 تحميل السنكسار</button>
            <button id="todayBtn" class="today-btn">📅 اليوم</button>
        </div>

        <div class="content-area" id="contentArea">
            <div class="loading-message">
                👑 اختر التاريخ لعرض سنكسار اليوم 👑<br>
                <small style="font-size: 1.2rem; color: #666;">أو اضغط على "اليوم" لعرض سنكسار اليوم الحالي</small>
            </div>
        </div>


    </div>

    <script src="synaxarium.js"></script>
</body>
</html>
