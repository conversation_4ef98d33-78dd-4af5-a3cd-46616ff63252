// صفحة الترحيب لبرنامج الكنيسة الأجبية والصلوات

document.addEventListener('DOMContentLoaded', function() {
    
    // التحقق من اكتمال التسجيل
    const registrationComplete = localStorage.getItem('church_registration_complete');
    if (!registrationComplete) {
        alert('يجب إكمال التسجيل أولاً');
        window.location.href = 'registration.html';
        return;
    }

    // استرجاع بيانات المستخدم
    const userDataStr = localStorage.getItem('church_user_data');
    if (!userDataStr) {
        alert('لم يتم العثور على بيانات المستخدم');
        window.location.href = 'registration.html';
        return;
    }

    const userData = JSON.parse(userDataStr);
    
    // تدليع الاسم
    function getNickname(fullName) {
        const names = fullName.trim().split(' ');
        const firstName = names[0];
        
        // قائمة بأسماء مدللة شائعة
        const nicknames = {
            'محمد': 'حمودي',
            'أحمد': 'حمودة',
            'علي': 'علوش',
            'حسن': 'حسونة',
            'محمود': 'حمودي',
            'عبدالله': 'عبدو',
            'يوسف': 'يويو',
            'عمر': 'عمورة',
            'مريم': 'ميمي',
            'فاطمة': 'فوفو',
            'عائشة': 'عوشة',
            'خديجة': 'خوخة',
            'زينب': 'زوزو',
            'سارة': 'سوسو',
            'نور': 'نونو',
            'ياسمين': 'يويو',
            'مينا': 'مينو',
            'جورج': 'جوجو',
            'مارك': 'ماركو',
            'بولس': 'بولو',
            'بطرس': 'بيتر',
            'يوحنا': 'جونو',
            'مريم': 'ميري',
            'مارتا': 'مارتو',
            'إليزابيث': 'ليزي',
            'كريستين': 'كريس',
            'أنطونيوس': 'طونو',
            'جرجس': 'جوجو',
            'كيرلس': 'كيرو',
            'شنودة': 'شنودي',
            'تكلا': 'تيكا',
            'فيرونيكا': 'فيرو',
            'كاترين': 'كاتي'
        };
        
        return nicknames[firstName] || firstName;
    }

    // تحديد التحية حسب الوقت
    function getTimeGreeting() {
        const hour = new Date().getHours();
        
        if (hour >= 5 && hour < 12) {
            return 'صباح الخير';
        } else if (hour >= 12 && hour < 17) {
            return 'مساء الخير';
        } else if (hour >= 17 && hour < 21) {
            return 'مساء الخير';
        } else {
            return 'مساء الخير';
        }
    }

    // تحديد الرمز حسب النوع
    function getGenderIcon(gender) {
        return gender === 'male' ? '👨' : '👩';
    }

    // حساب العمر
    function calculateAge(birthDate) {
        const today = new Date();
        const birth = new Date(birthDate);
        let age = today.getFullYear() - birth.getFullYear();
        const monthDiff = today.getMonth() - birth.getMonth();
        
        if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
            age--;
        }
        
        return age;
    }

    // تنسيق التاريخ
    function formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('ar-EG', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
    }

    // عرض بيانات المستخدم
    function displayUserData() {
        const nickname = getNickname(userData.name);
        const greeting = getTimeGreeting();
        const genderIcon = getGenderIcon(userData.gender);
        const age = calculateAge(userData.birthDate);
        
        // تحديث العناصر
        document.getElementById('userName').textContent = `${greeting} ${userData.name}`;
        document.getElementById('userNickname').textContent = `أهلاً وسهلاً ${nickname} الحبيب ${genderIcon}`;
        
        // عرض معلومات المستخدم
        const userInfoContainer = document.getElementById('userInfo');
        userInfoContainer.innerHTML = `
            <div class="info-item">
                <span class="info-label">الاسم:</span>
                <span class="info-value">${userData.name}</span>
            </div>
            <div class="info-item">
                <span class="info-label">الاسم المدلل:</span>
                <span class="info-value">${nickname}</span>
            </div>
            <div class="info-item">
                <span class="info-label">العمر:</span>
                <span class="info-value">${age} سنة</span>
            </div>
            <div class="info-item">
                <span class="info-label">تاريخ الميلاد:</span>
                <span class="info-value">${formatDate(userData.birthDate)}</span>
            </div>
            <div class="info-item">
                <span class="info-label">النوع:</span>
                <span class="info-value">${userData.gender === 'male' ? 'ذكر' : 'أنثى'} ${genderIcon}</span>
            </div>
            <div class="info-item">
                <span class="info-label">رقم الهاتف:</span>
                <span class="info-value">${userData.phone}</span>
            </div>
            <div class="info-item">
                <span class="info-label">الصورة الشخصية:</span>
                <span class="info-value">${userData.hasPhoto ? 'تم الرفع ✅' : 'لم يتم الرفع'}</span>
            </div>
            <div class="info-item">
                <span class="info-label">تاريخ التسجيل:</span>
                <span class="info-value">${formatDate(localStorage.getItem('church_registration_date'))}</span>
            </div>
        `;
    }

    // تأثيرات تفاعلية للرموز
    document.querySelectorAll('.qr-code').forEach(qr => {
        qr.addEventListener('click', function() {
            const title = this.getAttribute('title');
            
            // تأثير النقر
            this.style.transform = 'scale(0.9)';
            setTimeout(() => {
                this.style.transform = 'scale(1.1)';
                setTimeout(() => {
                    this.style.transform = 'scale(1)';
                }, 100);
            }, 100);
            
            // عرض رسالة
            showNotification(`تم النقر على: ${title}`);
        });
    });

    // عرض إشعار
    function showNotification(message) {
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(39, 174, 96, 0.9);
            color: white;
            padding: 15px 25px;
            border-radius: 25px;
            font-weight: 600;
            z-index: 10000;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            font-family: 'Cairo', sans-serif;
        `;
        notification.textContent = message;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.style.opacity = '0';
            notification.style.transform = 'translateX(-50%) translateY(-20px)';
            notification.style.transition = 'all 0.3s ease';
            
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 2000);
    }

    // حفظ إحصائيات الاستخدام
    function saveUsageStats() {
        const stats = {
            lastVisit: new Date().toISOString(),
            visitCount: parseInt(localStorage.getItem('church_app_visit_count') || '0') + 1,
            userAgent: navigator.userAgent,
            screenSize: `${screen.width}x${screen.height}`
        };
        
        localStorage.setItem('church_app_visit_count', stats.visitCount.toString());
        localStorage.setItem('church_app_last_visit', stats.lastVisit);
        localStorage.setItem('church_app_stats', JSON.stringify(stats));
        
        console.log(`إحصائيات الاستخدام - الزيارة رقم: ${stats.visitCount}`);
    }

    // رسائل ترحيب متنوعة
    function getRandomWelcomeMessage() {
        const messages = [
            'نورت البرنامج وأضأت طريقنا! 🌟',
            'أهلاً وسهلاً بك في بيتك الثاني! 🏠',
            'مرحباً بالعضو الجديد في عائلتنا! 👨‍👩‍👧‍👦',
            'سعداء جداً بانضمامك إلينا! 😊',
            'بركة الرب تحل عليك وعلى بيتك! 🙏',
            'نتمنى لك رحلة روحية مباركة! ✨'
        ];
        
        return messages[Math.floor(Math.random() * messages.length)];
    }

    // تحديث رسالة الترحيب
    setTimeout(() => {
        const welcomeMsg = document.querySelector('.welcome-message');
        const randomMessage = getRandomWelcomeMessage();
        welcomeMsg.innerHTML += `<br><br><strong style="color: #27ae60;">${randomMessage}</strong>`;
    }, 2000);

    // تشغيل الوظائف
    displayUserData();
    saveUsageStats();
    
    // رسالة ترحيب في وحدة التحكم
    console.log(`🎉 مرحباً ${userData.name}! تم تحميل صفحة الترحيب بنجاح`);
    console.log(`📱 برنامج الكنيسة الأجبية والصلوات - نسخة 1.0`);
});

// وظيفة بدء التطبيق
function startApp() {
    const btn = document.querySelector('.continue-btn');
    
    // تأثير التحميل
    btn.textContent = 'جاري التحضير...';
    btn.disabled = true;
    btn.style.background = 'linear-gradient(45deg, #95a5a6, #bdc3c7)';
    
    // محاكاة التحميل
    setTimeout(() => {
        btn.textContent = 'تم! جاري الانتقال...';
        
        setTimeout(() => {
            // يمكن الانتقال لصفحة التطبيق الرئيسية هنا
            alert('مرحباً بك! سيتم إضافة المزيد من الصفحات قريباً 🚀');
            
            // إعادة تعيين الزر
            btn.textContent = '🚀 ابدأ رحلتك الروحية';
            btn.disabled = false;
            btn.style.background = 'linear-gradient(45deg, #27ae60, #2ecc71)';
        }, 1500);
    }, 1000);
}
