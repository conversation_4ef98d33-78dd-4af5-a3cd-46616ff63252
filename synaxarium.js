// نظام السنكسار اليومي

document.addEventListener('DOMContentLoaded', function() {
    const dateInput = document.getElementById('dateInput');
    const loadBtn = document.getElementById('loadBtn');
    const todayBtn = document.getElementById('todayBtn');
    const contentArea = document.getElementById('contentArea');

    // تعيين التاريخ الحالي
    const today = new Date();
    dateInput.value = today.toISOString().split('T')[0];

    // بيانات السنكسار (عينة من البيانات)
    const synaxariumData = {
        '01-01': {
            title: 'أول يناير - رأس السنة الميلادية',
            content: `
                <div class="saint-name">ختان ربنا يسوع المسيح</div>
                في مثل هذا اليوم تم ختان ربنا يسوع المسيح بالجسد، وذلك بعد ثمانية أيام من ميلاده المبارك، كما هو مكتوب في الإنجيل المقدس: "ولما تمت ثمانية أيام ليختتن الصبي سمي يسوع، كما تسمى من الملاك قبل أن حبل به في البطن" (لوقا 2: 21).
                
                وقد خضع السيد المسيح لناموس الختان ليتمم كل بر، وليظهر أنه أخذ جسداً حقيقياً من العذراء مريم، وأنه وُلد تحت الناموس ليفتدي الذين تحت الناموس.
                
                <div class="saint-name">الشهيد باسيليوس</div>
                في هذا اليوم أيضاً استشهد القديس باسيليوس، الذي كان من أهل أنصنا. وقد عذبه الوالي عذاباً شديداً من أجل إيمانه بالمسيح، ولكنه احتمل العذاب بصبر عظيم حتى نال إكليل الشهادة.
                
                صلواتهم تكون معنا. آمين.
            `
        },
        '01-07': {
            title: '7 يناير - عيد الميلاد المجيد',
            content: `
                <div class="saint-name">عيد الميلاد المجيد</div>
                في مثل هذا اليوم وُلد ربنا وإلهنا ومخلصنا يسوع المسيح من العذراء القديسة مريم في بيت لحم اليهودية، في أيام هيرودس الملك، كما تنبأ عنه الأنبياء.
                
                وقد وُلد في مذود للبهائم لأنه لم يكن لهم موضع في المنزل، وظهر ملاك الرب للرعاة وبشرهم بالميلاد المبارك قائلاً: "لا تخافوا، فها أنا أبشركم بفرح عظيم يكون لجميع الشعب، أنه وُلد لكم اليوم في مدينة داود مخلص هو المسيح الرب".
                
                وجاء المجوس من المشرق ليسجدوا للطفل يسوع وقدموا له هدايا: ذهباً ولباناً ومراً. الذهب لأنه ملك، واللبان لأنه إله، والمر لأنه سيموت من أجل خلاص البشر.
                
                فرحة الميلاد تملأ قلوبنا، ونسبح الله الذي أحبنا هكذا حتى بذل ابنه الوحيد لكي لا يهلك كل من يؤمن به بل تكون له الحياة الأبدية.
            `
        },
        '01-19': {
            title: '19 يناير - عيد الغطاس المجيد',
            content: `
                <div class="saint-name">عيد الغطاس المجيد</div>
                في مثل هذا اليوم اعتمد ربنا يسوع المسيح من يوحنا المعمدان في نهر الأردن، وكان عمره ثلاثين سنة.
                
                ولما اعتمد يسوع صعد للوقت من الماء، وإذا السموات قد انفتحت له، فرأى روح الله نازلاً مثل حمامة وآتياً عليه، وصوت من السموات قائلاً: "هذا هو ابني الحبيب الذي به سررت".
                
                وبهذا ظهر الثالوث القدوس: الآب بالصوت، والابن بالجسد، والروح القدس بشكل حمامة. وقد اعتمد المسيح ليقدس المياه ويعطينا مثالاً للمعمودية المقدسة.
                
                <div class="saint-name">القديس يوحنا المعمدان</div>
                نذكر في هذا اليوم أيضاً القديس يوحنا المعمدان، السابق والشاهد للمسيح، الذي قال عنه الرب: "لم يقم بين المولودين من النساء أعظم من يوحنا المعمدان".
            `
        }
    };

    // دالة تحويل التاريخ إلى تنسيق MM-DD
    function formatDateKey(date) {
        const d = new Date(date);
        const month = String(d.getMonth() + 1).padStart(2, '0');
        const day = String(d.getDate()).padStart(2, '0');
        return `${month}-${day}`;
    }

    // دالة تحويل التاريخ إلى تنسيق عربي
    function formatArabicDate(date) {
        const d = new Date(date);
        const months = [
            'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
            'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
        ];
        
        const day = d.getDate();
        const month = months[d.getMonth()];
        const year = d.getFullYear();
        
        return `${day} ${month} ${year}`;
    }

    // دالة تحميل السنكسار
    function loadSynaxarium(date) {
        const dateKey = formatDateKey(date);
        const arabicDate = formatArabicDate(date);
        
        // عرض رسالة التحميل
        contentArea.innerHTML = `
            <div class="loading-message">
                ⏳ جاري تحميل سنكسار يوم ${arabicDate}...
            </div>
        `;

        // محاكاة تحميل البيانات
        setTimeout(() => {
            if (synaxariumData[dateKey]) {
                const data = synaxariumData[dateKey];
                contentArea.innerHTML = `
                    <div class="synaxarium-title">${data.title}</div>
                    <div class="synaxarium-content">${data.content}</div>
                `;
            } else {
                // محاولة جلب البيانات من مصدر خارجي (محاكاة)
                loadFromExternalSource(arabicDate);
            }
        }, 1500);
    }

    // دالة جلب البيانات من مصدر خارجي
    function loadFromExternalSource(arabicDate) {
        // محاولة جلب البيانات من موقع الأنبا تكلا
        const dateKey = formatDateKey(new Date(dateInput.value));

        // بيانات سنكسار إضافية
        const extendedSynaxariumData = {
            '01-02': {
                title: '2 يناير',
                content: `
                    <div class="saint-name">نياحة القديس سلبستروس بابا روما</div>
                    في مثل هذا اليوم من سنة 335م تنيح القديس سلبستروس بابا روما الثالث والثلاثون. وُلد هذا القديس في روما من أبوين مسيحيين تقيين، ونشأ في تقوى الله ومخافته. رُسم شماساً ثم قساً، وكان يعظ الشعب ويرشدهم إلى طريق الخلاص.

                    لما تولى الملك قسطنطين الكبير الحكم وآمن بالمسيح، أكرم هذا القديس إكراماً عظيماً، وبنى له كنائس كثيرة في روما والقسطنطينية. وقد عقد في أيامه مجمع نيقية المسكوني الأول سنة 325م لمحاربة بدعة آريوس.

                    عاش هذا القديس في البابوية إحدى وعشرين سنة وأحد عشر شهراً وتسعة أيام، وتنيح بسلام. صلاته تكون معنا. آمين.

                    <div class="saint-name">استشهاد القديس مقاريوس الإسكندري</div>
                    في هذا اليوم أيضاً استشهد القديس مقاريوس الإسكندري. كان هذا القديس من أهل الإسكندرية، وكان تاجراً أميناً في تجارته، محباً للفقراء والمساكين، يطعم الجياع ويكسو العراة.

                    لما سمع بأوامر الملك دقلديانوس بعبادة الأوثان، ترك تجارته وذهب إلى الوالي معترفاً بإيمانه بالمسيح. فأمر الوالي بتعذيبه عذاباً شديداً، ولكن الرب قواه فاحتمل العذاب بصبر عظيم حتى أسلم روحه الطاهرة ونال إكليل الشهادة.

                    صلاته تكون معنا. آمين.
                `
            },
            '01-03': {
                title: '3 يناير',
                content: `
                    <div class="saint-name">نياحة القديس زوسيما</div>
                    في مثل هذا اليوم تنيح القديس زوسيما. كان هذا القديس راهباً في أحد أديرة فلسطين، وكان مشهوراً بالتقوى والورع والعبادة. قضى حياته في الصوم والصلاة والتأمل في كلام الله.

                    اشتهر هذا القديس بلقائه مع القديسة مريم المصرية في البرية، حيث كانت تعيش في توبة ونسك شديد. وقد خدمها وناولها الأسرار المقدسة قبل نياحتها.

                    عاش القديس زوسيما حياة مقدسة مليئة بالفضائل، وتنيح بسلام في شيخوخة صالحة. صلاته تكون معنا. آمين.

                    <div class="saint-name">تذكار معجزة القديس باسيليوس الكبير</div>
                    في هذا اليوم نتذكر معجزة عظيمة للقديس باسيليوس الكبير أسقف قيصرية الكبادوك. حدث أن ملكاً وثنياً أراد أن يهدم كنيسة، فصلى القديس باسيليوس إلى الله، فأرسل الله ناراً من السماء أحرقت جنود الملك، فآمن الملك وكل جيشه بالمسيح.

                    هذه المعجزة تظهر قوة الصلاة وإيمان القديسين. صلاة القديس باسيليوس تكون معنا. آمين.
                `
            },
            '01-04': {
                title: '4 يناير',
                content: `
                    <div class="saint-name">نياحة القديس ثاؤفيلس البطريرك الثالث والعشرين</div>
                    في مثل هذا اليوم من سنة 412م تنيح القديس ثاؤفيلس البطريرك الثالث والعشرون من بطاركة الإسكندرية. كان هذا القديس عالماً فاضلاً وراعياً أميناً لشعبه.

                    اهتم بتعليم الشعب وإرشادهم إلى طريق الخلاص، وحارب البدع والهرطقات التي ظهرت في عصره. بنى كنائس كثيرة وأديرة للرهبان والراهبات.

                    كان محباً للفقراء والمساكين، ينفق على احتياجاتهم من أموال الكنيسة. عاش حياة مقدسة وتنيح بسلام بعد أن رعى الكنيسة بأمانة. صلاته تكون معنا. آمين.

                    <div class="saint-name">استشهاد القديس أبيفانيوس</div>
                    في هذا اليوم أيضاً استشهد القديس أبيفانيوس. كان من أهل منف، وكان شاباً مؤمناً تقياً. لما سمع بأوامر الاضطهاد، ذهب إلى الوالي معترفاً بإيمانه بالمسيح.

                    عذبه الوالي عذاباً شديداً، ولكنه احتمل العذاب بصبر وشجاعة، وأخيراً قطع رأسه فنال إكليل الشهادة. صلاته تكون معنا. آمين.
                `
            }
        };

        if (extendedSynaxariumData[dateKey]) {
            const data = extendedSynaxariumData[dateKey];
            contentArea.innerHTML = `
                <div class="synaxarium-title">سنكسار يوم ${arabicDate}</div>
                <div class="synaxarium-content">${data.content}</div>
            `;
        } else {
            // عرض محتوى عام للأيام غير المتوفرة
            const generalContent = `
                <div class="saint-name">سنكسار يوم ${arabicDate}</div>
                في مثل هذا اليوم من كل عام، تحتفل الكنيسة القبطية الأرثوذكسية بتذكار القديسين والشهداء والآباء الأطهار الذين انتقلوا إلى الأمجاد السماوية.

                <div class="saint-name">من تقليد الكنيسة</div>
                السنكسار هو كتاب يحتوي على سير القديسين والشهداء مرتبة حسب أيام السنة القبطية. وهو مصدر روحي غني يساعدنا على التأمل في حياة القديسين والاقتداء بفضائلهم.

                <div class="saint-name">فائدة روحية</div>
                إن قراءة سير القديسين تشجعنا على السير في طريق القداسة، وتعلمنا كيف نحيا حياة مسيحية حقيقية مليئة بالإيمان والرجاء والمحبة.

                <div class="saint-name">صلاة</div>
                يا رب، بشفاعة جميع القديسين الذين انتقلوا في مثل هذا اليوم، احفظنا واحرسنا من كل شر، وأعنا على السير في طريق القداسة والبر. بالمسيح يسوع ربنا. آمين.

                <div class="saint-name">بركة القديسين</div>
                صلوات جميع القديسين والشهداء والآباء الأطهار تكون معنا جميعاً. آمين.
            `;

            contentArea.innerHTML = `
                <div class="synaxarium-title">سنكسار يوم ${arabicDate}</div>
                <div class="synaxarium-content">${generalContent}</div>
            `;
        }
    }

    // معالج زر التحميل
    loadBtn.addEventListener('click', function() {
        const selectedDate = dateInput.value;
        if (selectedDate) {
            loadSynaxarium(selectedDate);
        } else {
            contentArea.innerHTML = `
                <div class="error-message">
                    ⚠️ يرجى اختيار تاريخ أولاً
                </div>
            `;
        }
    });

    // معالج زر اليوم
    todayBtn.addEventListener('click', function() {
        const today = new Date().toISOString().split('T')[0];
        dateInput.value = today;
        loadSynaxarium(today);
    });

    // معالج تغيير التاريخ
    dateInput.addEventListener('change', function() {
        if (this.value) {
            loadSynaxarium(this.value);
        }
    });

    // تحميل سنكسار اليوم تلقائياً
    setTimeout(() => {
        const today = new Date().toISOString().split('T')[0];
        loadSynaxarium(today);
    }, 1000);

    console.log('👑 تم تحميل صفحة السنكسار اليومي بنجاح!');
});
