// نظام السنكسار اليومي

document.addEventListener('DOMContentLoaded', function() {
    const dateInput = document.getElementById('dateInput');
    const loadBtn = document.getElementById('loadBtn');
    const todayBtn = document.getElementById('todayBtn');
    const contentArea = document.getElementById('contentArea');

    // تعيين التاريخ الحالي
    const today = new Date();
    dateInput.value = today.toISOString().split('T')[0];

    // بيانات السنكسار (عينة من البيانات)
    const synaxariumData = {
        '01-01': {
            title: 'أول يناير - رأس السنة الميلادية',
            content: `
                <div class="saint-name">ختان ربنا يسوع المسيح</div>
                في مثل هذا اليوم تم ختان ربنا يسوع المسيح بالجسد، وذلك بعد ثمانية أيام من ميلاده المبارك، كما هو مكتوب في الإنجيل المقدس: "ولما تمت ثمانية أيام ليختتن الصبي سمي يسوع، كما تسمى من الملاك قبل أن حبل به في البطن" (لوقا 2: 21).
                
                وقد خضع السيد المسيح لناموس الختان ليتمم كل بر، وليظهر أنه أخذ جسداً حقيقياً من العذراء مريم، وأنه وُلد تحت الناموس ليفتدي الذين تحت الناموس.
                
                <div class="saint-name">الشهيد باسيليوس</div>
                في هذا اليوم أيضاً استشهد القديس باسيليوس، الذي كان من أهل أنصنا. وقد عذبه الوالي عذاباً شديداً من أجل إيمانه بالمسيح، ولكنه احتمل العذاب بصبر عظيم حتى نال إكليل الشهادة.
                
                صلواتهم تكون معنا. آمين.
            `
        },
        '01-07': {
            title: '7 يناير - عيد الميلاد المجيد',
            content: `
                <div class="saint-name">عيد الميلاد المجيد</div>
                في مثل هذا اليوم وُلد ربنا وإلهنا ومخلصنا يسوع المسيح من العذراء القديسة مريم في بيت لحم اليهودية، في أيام هيرودس الملك، كما تنبأ عنه الأنبياء.
                
                وقد وُلد في مذود للبهائم لأنه لم يكن لهم موضع في المنزل، وظهر ملاك الرب للرعاة وبشرهم بالميلاد المبارك قائلاً: "لا تخافوا، فها أنا أبشركم بفرح عظيم يكون لجميع الشعب، أنه وُلد لكم اليوم في مدينة داود مخلص هو المسيح الرب".
                
                وجاء المجوس من المشرق ليسجدوا للطفل يسوع وقدموا له هدايا: ذهباً ولباناً ومراً. الذهب لأنه ملك، واللبان لأنه إله، والمر لأنه سيموت من أجل خلاص البشر.
                
                فرحة الميلاد تملأ قلوبنا، ونسبح الله الذي أحبنا هكذا حتى بذل ابنه الوحيد لكي لا يهلك كل من يؤمن به بل تكون له الحياة الأبدية.
            `
        },
        '01-19': {
            title: '19 يناير - عيد الغطاس المجيد',
            content: `
                <div class="saint-name">عيد الغطاس المجيد</div>
                في مثل هذا اليوم اعتمد ربنا يسوع المسيح من يوحنا المعمدان في نهر الأردن، وكان عمره ثلاثين سنة.
                
                ولما اعتمد يسوع صعد للوقت من الماء، وإذا السموات قد انفتحت له، فرأى روح الله نازلاً مثل حمامة وآتياً عليه، وصوت من السموات قائلاً: "هذا هو ابني الحبيب الذي به سررت".
                
                وبهذا ظهر الثالوث القدوس: الآب بالصوت، والابن بالجسد، والروح القدس بشكل حمامة. وقد اعتمد المسيح ليقدس المياه ويعطينا مثالاً للمعمودية المقدسة.
                
                <div class="saint-name">القديس يوحنا المعمدان</div>
                نذكر في هذا اليوم أيضاً القديس يوحنا المعمدان، السابق والشاهد للمسيح، الذي قال عنه الرب: "لم يقم بين المولودين من النساء أعظم من يوحنا المعمدان".
            `
        }
    };

    // دالة تحويل التاريخ إلى تنسيق MM-DD
    function formatDateKey(date) {
        const d = new Date(date);
        const month = String(d.getMonth() + 1).padStart(2, '0');
        const day = String(d.getDate()).padStart(2, '0');
        return `${month}-${day}`;
    }

    // دالة تحويل التاريخ إلى تنسيق عربي
    function formatArabicDate(date) {
        const d = new Date(date);
        const months = [
            'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
            'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
        ];
        
        const day = d.getDate();
        const month = months[d.getMonth()];
        const year = d.getFullYear();
        
        return `${day} ${month} ${year}`;
    }

    // دالة تحميل السنكسار
    function loadSynaxarium(date) {
        const dateKey = formatDateKey(date);
        const arabicDate = formatArabicDate(date);
        
        // عرض رسالة التحميل
        contentArea.innerHTML = `
            <div class="loading-message">
                ⏳ جاري تحميل سنكسار يوم ${arabicDate}...
            </div>
        `;

        // محاكاة تحميل البيانات
        setTimeout(() => {
            if (synaxariumData[dateKey]) {
                const data = synaxariumData[dateKey];
                contentArea.innerHTML = `
                    <div class="synaxarium-title">${data.title}</div>
                    <div class="synaxarium-content">${data.content}</div>
                `;
            } else {
                // محاولة جلب البيانات من مصدر خارجي (محاكاة)
                loadFromExternalSource(arabicDate);
            }
        }, 1500);
    }

    // دالة محاكاة جلب البيانات من مصدر خارجي
    function loadFromExternalSource(arabicDate) {
        // هنا يمكن إضافة كود لجلب البيانات من موقع الأنبا تكلا
        // لكن بسبب قيود CORS، سنعرض رسالة بديلة
        
        const sampleContent = `
            <div class="saint-name">سنكسار يوم ${arabicDate}</div>
            نعتذر، البيانات غير متوفرة حالياً لهذا التاريخ. يمكنك زيارة موقع الأنبا تكلا هيمانوت مباشرة للاطلاع على السنكسار الكامل.
            
            <div class="saint-name">ملاحظة</div>
            يحتوي السنكسار على سير القديسين والشهداء والآباء الأطهار الذين انتقلوا في مثل هذا اليوم عبر التاريخ المسيحي. وهو مصدر روحي غني يساعدنا على التأمل في حياة القديسين والاقتداء بهم.
            
            <div class="saint-name">صلاة</div>
            يا رب، بشفاعة جميع القديسين الذين انتقلوا في مثل هذا اليوم، احفظنا واحرسنا من كل شر، وأعنا على السير في طريق القداسة. آمين.
        `;

        contentArea.innerHTML = `
            <div class="synaxarium-title">سنكسار يوم ${arabicDate}</div>
            <div class="synaxarium-content">${sampleContent}</div>
        `;
    }

    // معالج زر التحميل
    loadBtn.addEventListener('click', function() {
        const selectedDate = dateInput.value;
        if (selectedDate) {
            loadSynaxarium(selectedDate);
        } else {
            contentArea.innerHTML = `
                <div class="error-message">
                    ⚠️ يرجى اختيار تاريخ أولاً
                </div>
            `;
        }
    });

    // معالج زر اليوم
    todayBtn.addEventListener('click', function() {
        const today = new Date().toISOString().split('T')[0];
        dateInput.value = today;
        loadSynaxarium(today);
    });

    // معالج تغيير التاريخ
    dateInput.addEventListener('change', function() {
        if (this.value) {
            loadSynaxarium(this.value);
        }
    });

    // تحميل سنكسار اليوم تلقائياً
    setTimeout(() => {
        const today = new Date().toISOString().split('T')[0];
        loadSynaxarium(today);
    }, 1000);

    console.log('👑 تم تحميل صفحة السنكسار اليومي بنجاح!');
});
