// الصفحة الرئيسية لبرنامج الكنيسة الأجبية والصلوات

document.addEventListener('DOMContentLoaded', function() {
    
    // استرجاع بيانات المستخدم
    const userDataStr = localStorage.getItem('church_user_data');
    let userData = null;
    
    if (userDataStr) {
        userData = JSON.parse(userDataStr);
        displayUserInfo(userData);
    }

    // عرض معلومات المستخدم
    function displayUserInfo(userData) {
        const nickname = getNickname(userData.name);
        const greeting = getTimeGreeting();
        
        document.getElementById('userName').textContent = `${greeting} ${userData.name}`;
        document.getElementById('userNickname').textContent = `أهلاً ${nickname} الحبيب`;
    }

    // تدليع الاسم
    function getNickname(fullName) {
        const names = fullName.trim().split(' ');
        const firstName = names[0];
        
        const nicknames = {
            'محمد': 'حمودي', 'أحمد': 'حمودة', 'علي': 'علوش', 'حسن': 'حسونة',
            'محمود': 'حمودي', 'عبدالله': 'عبدو', 'يوسف': 'يويو', 'عمر': 'عمورة',
            'مريم': 'ميمي', 'فاطمة': 'فوفو', 'عائشة': 'عوشة', 'خديجة': 'خوخة',
            'زينب': 'زوزو', 'سارة': 'سوسو', 'نور': 'نونو', 'ياسمين': 'يويو',
            'مينا': 'مينو', 'جورج': 'جوجو', 'مارك': 'ماركو', 'بولس': 'بولو',
            'بطرس': 'بيتر', 'يوحنا': 'جونو', 'مريم': 'ميري', 'مارتا': 'مارتو',
            'إليزابيث': 'ليزي', 'كريستين': 'كريس', 'أنطونيوس': 'طونو',
            'جرجس': 'جوجو', 'كيرلس': 'كيرو', 'شنودة': 'شنودي'
        };
        
        return nicknames[firstName] || firstName;
    }

    // تحديد التحية حسب الوقت
    function getTimeGreeting() {
        const hour = new Date().getHours();
        if (hour >= 5 && hour < 12) return 'صباح الخير';
        else if (hour >= 12 && hour < 17) return 'مساء الخير';
        else if (hour >= 17 && hour < 21) return 'مساء الخير';
        else return 'مساء الخير';
    }

    // متغيرات التحكم في الأقسام
    const sectionsContainer = document.getElementById('sectionsContainer');
    const sections = document.querySelectorAll('.app-section');

    // تأثيرات الأقسام
    function addSectionEffects() {
        sections.forEach((section, index) => {
            // تأثير الظهور التدريجي
            section.style.opacity = '0';
            section.style.transform = 'translateY(30px)';

            setTimeout(() => {
                section.style.transition = 'all 0.6s ease';
                section.style.opacity = '1';
                section.style.transform = 'translateY(0)';
            }, (index + 1) * 150);
        });
    }

    // معالج النقر على الأقسام
    sections.forEach(section => {
        section.addEventListener('click', function() {
            const sectionType = this.getAttribute('data-section');
            handleSectionClick(sectionType, this);
        });
    });
    // إنشاء رموز QR حقيقية
    function generateQRCode(container, data) {
        // نمط QR مبسط 21x21
        const qrPatterns = {
            'QR001': [
                '111111101010101111111',
                '100000101110101000001',
                '101110101010101011101',
                '101110100110101011101',
                '101110101010101011101',
                '100000101110101000001',
                '111111101010101111111',
                '000000001110100000000',
                '101010111010111010101',
                '010101010101010101010',
                '111010101010101010111',
                '010101010101010101010',
                '101010111010111010101',
                '000000001110100000000',
                '111111101010101111111',
                '100000101110101000001',
                '101110101010101011101',
                '101110100110101011101',
                '101110101010101011101',
                '100000101110101000001',
                '111111101010101111111'
            ],
            'QR002': [
                '111111100110101111111',
                '100000101010101000001',
                '101110101110101011101',
                '101110101010101011101',
                '101110100110101011101',
                '100000101010101000001',
                '111111100110101111111',
                '000000001010100000000',
                '110101111110111010110',
                '010101010101010101010',
                '101010111010111010101',
                '010101010101010101010',
                '110101111110111010110',
                '000000001010100000000',
                '111111100110101111111',
                '100000101010101000001',
                '101110101110101011101',
                '101110101010101011101',
                '101110100110101011101',
                '100000101010101000001',
                '111111100110101111111'
            ],
            'QR003': [
                '111111101110101111111',
                '100000100010101000001',
                '101110101010101011101',
                '101110101110101011101',
                '101110101010101011101',
                '100000100010101000001',
                '111111101110101111111',
                '000000000010100000000',
                '101011111010111010101',
                '010101010101010101010',
                '111010101110101010111',
                '010101010101010101010',
                '101011111010111010101',
                '000000000010100000000',
                '111111101110101111111',
                '100000100010101000001',
                '101110101010101011101',
                '101110101110101011101',
                '101110101010101011101',
                '100000100010101000001',
                '111111101110101111111'
            ],
            'QR004': [
                '111111100010101111111',
                '100000101110101000001',
                '101110100010101011101',
                '101110101110101011101',
                '101110101010101011101',
                '100000101110101000001',
                '111111100010101111111',
                '000000001110100000000',
                '110101110010111010110',
                '010101010101010101010',
                '101010101110101010101',
                '010101010101010101010',
                '110101110010111010110',
                '000000001110100000000',
                '111111100010101111111',
                '100000101110101000001',
                '101110100010101011101',
                '101110101110101011101',
                '101110101010101011101',
                '100000101110101000001',
                '111111100010101111111'
            ],
            'QR005': [
                '111111101010101111111',
                '100000100110101000001',
                '101110101010101011101',
                '101110100010101011101',
                '101110101110101011101',
                '100000100110101000001',
                '111111101010101111111',
                '000000000110100000000',
                '101010111110111010101',
                '010101010101010101010',
                '111010100010101010111',
                '010101010101010101010',
                '101010111110111010101',
                '000000000110100000000',
                '111111101010101111111',
                '100000100110101000001',
                '101110101010101011101',
                '101110100010101011101',
                '101110101110101011101',
                '100000100110101000001',
                '111111101010101111111'
            ],
            'QR006': [
                '111111100110101111111',
                '100000101010101000001',
                '101110100110101011101',
                '101110101010101011101',
                '101110100110101011101',
                '100000101010101000001',
                '111111100110101111111',
                '000000001010100000000',
                '110101111010111010110',
                '010101010101010101010',
                '101010110110101010101',
                '010101010101010101010',
                '110101111010111010110',
                '000000001010100000000',
                '111111100110101111111',
                '100000101010101000001',
                '101110100110101011101',
                '101110101010101011101',
                '101110100110101011101',
                '100000101010101000001',
                '111111100110101111111'
            ],
            'QR007': [
                '111111101110101111111',
                '100000100010101000001',
                '101110101110101011101',
                '101110100010101011101',
                '101110101110101011101',
                '100000100010101000001',
                '111111101110101111111',
                '000000000010100000000',
                '101011110110111010101',
                '010101010101010101010',
                '111010101010101010111',
                '010101010101010101010',
                '101011110110111010101',
                '000000000010100000000',
                '111111101110101111111',
                '100000100010101000001',
                '101110101110101011101',
                '101110100010101011101',
                '101110101110101011101',
                '100000100010101000001',
                '111111101110101111111'
            ],
            'QR008': [
                '111111100010101111111',
                '100000101110101000001',
                '101110100110101011101',
                '101110101010101011101',
                '101110100110101011101',
                '100000101110101000001',
                '111111100010101111111',
                '000000001110100000000',
                '110101110110111010110',
                '010101010101010101010',
                '101010101010101010101',
                '010101010101010101010',
                '110101110110111010110',
                '000000001110100000000',
                '111111100010101111111',
                '100000101110101000001',
                '101110100110101011101',
                '101110101010101011101',
                '101110100110101011101',
                '100000101110101000001',
                '111111100010101111111'
            ]
        };

        const pattern = qrPatterns[data] || qrPatterns['QR001'];
        container.innerHTML = '';

        pattern.forEach(row => {
            row.split('').forEach(pixel => {
                const div = document.createElement('div');
                div.className = pixel === '1' ? 'qr-pixel' : 'qr-pixel white';
                container.appendChild(div);
            });
        });
    }

    // إنشاء رموز QR لجميع الأقسام
    document.querySelectorAll('.qr-code').forEach(qrElement => {
        const section = qrElement.closest('.app-section');
        const qrData = section.getAttribute('data-qr');
        generateQRCode(qrElement, qrData);
    });

    // تشغيل تأثيرات الأقسام
    addSectionEffects();

    // معالج النقر على الأقسام
    sections.forEach(section => {
        section.addEventListener('click', function() {
            const sectionType = this.getAttribute('data-section');
            handleSectionClick(sectionType, this);
        });
    });

    // معالجة النقر على الأقسام
    function handleSectionClick(sectionType, element) {
        // بدون تأثيرات - مباشرة للمحتوى

        // تحديد الإجراء حسب القسم مع الرموز المحدثة
        switch(sectionType) {
            case 'word-of-god':
                showSectionMessage('كلمة الله المحيي', 'آيات وتأملات من الكتاب المقدس لتغذية الروح والقلب', '📖');
                break;
            case 'seven-prayers':
                showSectionMessage('كتاب الـ7 صلوات', 'الصلوات السبع المقدسة للكنيسة القبطية الأرثوذكسية', '🕊️');
                break;
            case 'modern-media':
                showSectionMessage('ألعاب الميديا الحديثة', 'ألعاب تعليمية وترفيهية مسيحية للأطفال والكبار', '🎯');
                break;
            case 'fathers-sayings':
                showSectionMessage('أقوال الآباء الأولين', 'حكم وأقوال من آباء الكنيسة العظام والقديسين', '📜');
                break;
            case 'church-treasures':
                showSectionMessage('كنوز كنيستنا', 'تراث وكنوز الكنيسة القبطية الأرثوذكسية العريقة', '⛪');
                break;
            case 'daily-prayers':
                showSectionMessage('الصلوات اليومية', 'صلوات الساعات والأوقات المقدسة على مدار اليوم', '🌅');
                break;
            case 'hymns':
                showSectionMessage('التسابيح والألحان', 'تسابيح وألحان الكنيسة القبطية الجميلة', '🎼');
                break;
            case 'calendar':
                showSectionMessage('التقويم الكنسي', 'الأعياد والمناسبات والأصوام الكنسية', '🗓️');
                break;
        }

        // حفظ إحصائيات الاستخدام
        saveUsageStats(sectionType);
    }

    // عرض رسالة القسم
    function showSectionMessage(title, description, icon) {
        const modal = document.createElement('div');
        modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            backdrop-filter: blur(5px);
        `;

        modal.innerHTML = `
            <div style="
                background: white;
                padding: 40px;
                border-radius: 20px;
                text-align: center;
                max-width: 400px;
                box-shadow: 0 20px 50px rgba(0, 0, 0, 0.3);
                border: 3px solid #ffd700;
            ">
                <div style="font-size: 4rem; margin-bottom: 20px;">${icon}</div>
                <h2 style="color: #1e3c72; margin-bottom: 15px; font-family: 'Amiri', serif;">${title}</h2>
                <p style="color: #666; line-height: 1.6; margin-bottom: 25px;">${description}</p>
                <button onclick="this.parentElement.parentElement.remove()" style="
                    background: linear-gradient(45deg, #1e3c72, #2a5298);
                    color: white;
                    border: none;
                    padding: 12px 30px;
                    border-radius: 25px;
                    cursor: pointer;
                    font-family: 'Cairo', sans-serif;
                    font-weight: 600;
                ">حسناً</button>
            </div>
        `;

        document.body.appendChild(modal);

        // إزالة المودال عند النقر خارجه
        modal.addEventListener('click', function(e) {
            if (e.target === modal) {
                modal.remove();
            }
        });
    }

    // حفظ إحصائيات الاستخدام
    function saveUsageStats(section) {
        const stats = JSON.parse(localStorage.getItem('church_app_section_stats') || '{}');
        stats[section] = (stats[section] || 0) + 1;
        stats.lastAccessed = new Date().toISOString();
        localStorage.setItem('church_app_section_stats', JSON.stringify(stats));
    }

    // عرض رسالة الترحيب عند التحميل
    setTimeout(() => {
        document.getElementById('welcomeMessage').classList.add('show');
    }, 1000);

    // إخفاء رسالة الترحيب تلقائياً بعد 5 ثوان
    setTimeout(() => {
        closeWelcome();
    }, 6000);

    // معالج رموز QR مع معلومات مفصلة
    document.querySelectorAll('.qr-code').forEach(qr => {
        qr.addEventListener('click', function(e) {
            e.stopPropagation(); // منع تفعيل النقر على القسم

            // البحث عن القسم الأب
            const sectionElement = this.closest('.app-section');
            const qrCode = sectionElement.getAttribute('data-qr');
            const sectionType = sectionElement.getAttribute('data-section');
            const sectionTitle = sectionElement.querySelector('.section-title').textContent;

            showQRModal(qrCode, sectionTitle, sectionType);
        });
    });

    // عرض نافذة رمز الاستجابة السريعة
    function showQRModal(qrCode, title, sectionType) {
        const modal = document.createElement('div');
        modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 2000;
            backdrop-filter: blur(5px);
        `;

        modal.innerHTML = `
            <div style="
                background: white;
                padding: 40px;
                border-radius: 20px;
                text-align: center;
                max-width: 400px;
                box-shadow: 0 20px 50px rgba(0, 0, 0, 0.3);
                border: 3px solid #ffd700;
                position: relative;
            ">
                <button onclick="this.parentElement.parentElement.remove()" style="
                    position: absolute;
                    top: 10px;
                    left: 10px;
                    background: #e74c3c;
                    color: white;
                    border: none;
                    width: 30px;
                    height: 30px;
                    border-radius: 50%;
                    cursor: pointer;
                    font-size: 1.2rem;
                ">×</button>

                <div style="
                    width: 200px;
                    height: 200px;
                    background: #f8f9fa;
                    border: 2px solid #1e3c72;
                    border-radius: 15px;
                    margin: 0 auto 20px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 3rem;
                    position: relative;
                ">
                    <div style="
                        position: absolute;
                        top: 10px;
                        right: 10px;
                        background: #ffd700;
                        color: #1e3c72;
                        padding: 5px 10px;
                        border-radius: 10px;
                        font-size: 0.8rem;
                        font-weight: bold;
                    ">${qrCode}</div>

                    <div style="
                        display: grid;
                        grid-template-columns: repeat(8, 1fr);
                        grid-template-rows: repeat(8, 1fr);
                        gap: 2px;
                        width: 120px;
                        height: 120px;
                    ">
                        ${generateQRPattern(qrCode)}
                    </div>
                </div>

                <h3 style="color: #1e3c72; margin-bottom: 15px; font-family: 'Amiri', serif;">${title}</h3>
                <p style="color: #666; line-height: 1.6; margin-bottom: 20px;">
                    امسح هذا الرمز للوصول السريع إلى قسم "${title}"
                </p>
                <div style="
                    background: rgba(30, 60, 114, 0.1);
                    padding: 15px;
                    border-radius: 10px;
                    margin-bottom: 20px;
                ">
                    <strong>رقم القسم:</strong> ${qrCode}<br>
                    <strong>نوع المحتوى:</strong> ${getSectionDescription(sectionType)}
                </div>
                <button onclick="copyQRCode('${qrCode}')" style="
                    background: linear-gradient(45deg, #1e3c72, #2a5298);
                    color: white;
                    border: none;
                    padding: 12px 25px;
                    border-radius: 25px;
                    cursor: pointer;
                    font-family: 'Cairo', sans-serif;
                    font-weight: 600;
                    margin-right: 10px;
                ">نسخ الرمز</button>
                <button onclick="shareQRCode('${qrCode}', '${title}')" style="
                    background: linear-gradient(45deg, #27ae60, #2ecc71);
                    color: white;
                    border: none;
                    padding: 12px 25px;
                    border-radius: 25px;
                    cursor: pointer;
                    font-family: 'Cairo', sans-serif;
                    font-weight: 600;
                ">مشاركة</button>
            </div>
        `;

        document.body.appendChild(modal);

        // إزالة المودال عند النقر خارجه
        modal.addEventListener('click', function(e) {
            if (e.target === modal) {
                modal.remove();
            }
        });
    }

    // عرض إشعار
    function showNotification(message) {
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(39, 174, 96, 0.9);
            color: white;
            padding: 15px 25px;
            border-radius: 25px;
            font-weight: 600;
            z-index: 10000;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            font-family: 'Cairo', sans-serif;
        `;
        notification.textContent = message;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.style.opacity = '0';
            notification.style.transform = 'translateX(-50%) translateY(-20px)';
            notification.style.transition = 'all 0.3s ease';
            
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 2000);
    }

    // دعم لوحة المفاتيح
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            closeWelcome();
        }
        
        // أرقام 1-8 لاختيار الأقسام
        const num = parseInt(e.key);
        if (num >= 1 && num <= 8) {
            const section = document.querySelector(`.section-${num}`);
            if (section) {
                section.click();
            }
        }
    });

    console.log('🎉 تم تحميل الصفحة الرئيسية بنجاح!');
    console.log('💡 نصيحة: اسحب الدائرة لتدويرها أو انقر على رموز QR!');
});

// دوال مساعدة لرموز الاستجابة السريعة
function generateQRPattern(qrCode) {
    // إنشاء نمط QR بسيط بناءً على الرمز
    const patterns = {
        'QR001': '■□■□■□■□□■□■□■□■■□■□■□■□□■□■□■□■■□■□■□■□□■□■□■□■■□■□■□■□□■□■□■□■',
        'QR002': '□■□■□■□■■□■□■□■□□■□■□■□■■□■□■□■□□■□■□■□■■□■□■□■□□■□■□■□■■□■□■□■□',
        'QR003': '■■□□■■□□□□■■□□■■■■□□■■□□□□■■□□■■■■□□■■□□□□■■□□■■■■□□■■□□□□■■□□■■',
        'QR004': '□□■■□□■■■■□□■■□□□□■■□□■■■■□□■■□□□□■■□□■■■■□□■■□□□□■■□□■■■■□□■■□□',
        'QR005': '■□■■□■□■□■□■■□■□■□■■□■□■□■□■■□■□■□■■□■□■□■□■■□■□■□■■□■□■□■□■■□■□',
        'QR006': '□■□□■□■□■□■□□■□■□■□□■□■□■□■□□■□■□■□□■□■□■□■□□■□■□■□□■□■□■□■□□■□■',
        'QR007': '■■■□□■■■□□□■■■□□■■■□□■■■□□□■■■□□■■■□□■■■□□□■■■□□■■■□□■■■□□□■■■□□',
        'QR008': '□□□■■□□□■■■□□□■■□□□■■□□□■■■□□□■■□□□■■□□□■■■□□□■■□□□■■□□□■■■□□□■■'
    };

    const pattern = patterns[qrCode] || patterns['QR001'];
    return pattern.split('').map(bit =>
        `<div style="background: ${bit === '■' ? '#1e3c72' : '#f8f9fa'}; border-radius: 1px;"></div>`
    ).join('');
}

function getSectionDescription(sectionType) {
    const descriptions = {
        'word-of-god': 'آيات وتأملات من الكتاب المقدس',
        'seven-prayers': 'الصلوات السبع المقدسة',
        'modern-media': 'ألعاب تعليمية وترفيهية',
        'fathers-sayings': 'حكم وأقوال الآباء القديسين',
        'church-treasures': 'تراث وكنوز الكنيسة',
        'daily-prayers': 'صلوات الساعات اليومية',
        'hymns': 'تسابيح وألحان كنسية',
        'calendar': 'الأعياد والمناسبات الكنسية'
    };

    return descriptions[sectionType] || 'محتوى كنسي مميز';
}

function copyQRCode(qrCode) {
    const text = `رمز الاستجابة السريعة: ${qrCode}\nبرنامج الكنيسة الأجبية والصلوات`;

    if (navigator.clipboard) {
        navigator.clipboard.writeText(text).then(() => {
            showNotification('تم نسخ رمز الاستجابة السريعة! 📋');
        });
    } else {
        // للمتصفحات القديمة
        const textArea = document.createElement('textarea');
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        showNotification('تم نسخ رمز الاستجابة السريعة! 📋');
    }
}

function shareQRCode(qrCode, title) {
    const shareData = {
        title: `${title} - برنامج الكنيسة`,
        text: `تفضل بزيارة قسم "${title}" في برنامج الكنيسة الأجبية والصلوات`,
        url: window.location.href + `#${qrCode}`
    };

    if (navigator.share) {
        navigator.share(shareData).then(() => {
            showNotification('تم مشاركة الرمز بنجاح! 📤');
        }).catch(() => {
            fallbackShare(shareData);
        });
    } else {
        fallbackShare(shareData);
    }
}

function fallbackShare(shareData) {
    const text = `${shareData.text}\n${shareData.url}`;

    if (navigator.clipboard) {
        navigator.clipboard.writeText(text).then(() => {
            showNotification('تم نسخ رابط المشاركة! 🔗');
        });
    } else {
        showNotification('يمكنك نسخ الرابط يدوياً: ' + shareData.url);
    }
}

// إغلاق رسالة الترحيب
function closeWelcome() {
    const welcomeMessage = document.getElementById('welcomeMessage');
    if (welcomeMessage) {
        welcomeMessage.classList.remove('show');
    }
}
