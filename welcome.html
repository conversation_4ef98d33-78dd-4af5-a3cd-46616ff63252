<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مرحباً بك - برنامج الكنيسة الأجبية والصلوات</title>
    <link href="https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="common-styles.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', '<PERSON>i', serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 50%, #87ceeb 100%);
            min-height: 100vh;
            color: #333;
            line-height: 1.8;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .welcome-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 25px;
            padding: 50px;
            box-shadow: 0 25px 60px rgba(0, 0, 0, 0.2);
            backdrop-filter: blur(15px);
            border: 3px solid rgba(255, 215, 0, 0.4);
            max-width: 600px;
            width: 100%;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .welcome-container::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255, 215, 0, 0.05) 0%, transparent 70%);
            pointer-events: none;
        }

        .logo {
            width: 120px;
            height: 120px;
            margin: 0 auto 30px;
            background: linear-gradient(45deg, #ffd700, #ffed4e);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 15px 40px rgba(255, 215, 0, 0.5);
            position: relative;
            z-index: 2;
        }

        .logo::before {
            content: '✞';
            font-size: 60px;
            color: #1e3c72;
            font-weight: bold;
            text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.3);
        }

        .welcome-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: #1e3c72;
            margin-bottom: 20px;
            font-family: 'Amiri', serif;
            position: relative;
            z-index: 2;
        }

        .user-name {
            font-size: 2rem;
            color: #ffd700;
            font-weight: 600;
            margin-bottom: 15px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
            position: relative;
            z-index: 2;
        }

        .nickname {
            font-size: 1.3rem;
            color: #666;
            margin-bottom: 30px;
            font-style: italic;
            position: relative;
            z-index: 2;
        }

        .welcome-message {
            font-size: 1.2rem;
            color: #555;
            margin-bottom: 30px;
            line-height: 1.8;
            position: relative;
            z-index: 2;
        }

        .verse-container {
            background: linear-gradient(45deg, rgba(255, 215, 0, 0.1), rgba(30, 60, 114, 0.1));
            padding: 25px;
            border-radius: 15px;
            margin: 30px 0;
            border-right: 5px solid #ffd700;
            position: relative;
            z-index: 2;
        }

        .verse {
            font-size: 1.1rem;
            color: #1e3c72;
            font-style: italic;
            font-weight: 500;
            margin-bottom: 10px;
        }

        .verse-reference {
            font-size: 0.9rem;
            color: #666;
            font-weight: 600;
        }

        .user-info {
            background: rgba(30, 60, 114, 0.05);
            padding: 20px;
            border-radius: 15px;
            margin: 25px 0;
            position: relative;
            z-index: 2;
        }

        .info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            padding: 8px 0;
            border-bottom: 1px solid rgba(30, 60, 114, 0.1);
        }

        .info-item:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }

        .info-label {
            font-weight: 600;
            color: #1e3c72;
        }

        .info-value {
            color: #666;
        }

        .continue-btn {
            background: linear-gradient(45deg, #27ae60, #2ecc71);
            color: white;
            border: none;
            padding: 18px 40px;
            font-size: 1.2rem;
            font-weight: 600;
            border-radius: 25px;
            cursor: pointer;
            box-shadow: 0 8px 25px rgba(39, 174, 96, 0.3);
            transition: all 0.3s ease;
            font-family: 'Cairo', sans-serif;
            margin-top: 20px;
            position: relative;
            z-index: 2;
        }

        .continue-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 35px rgba(39, 174, 96, 0.4);
        }

        .continue-btn:active {
            transform: translateY(-1px);
        }

        .qr-codes {
            position: fixed;
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .qr-code {
            width: 60px;
            height: 60px;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            font-size: 1.5rem;
            color: #1e3c72;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .qr-code:hover {
            transform: scale(1.1);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .qr-top-right {
            top: 20px;
            right: 20px;
        }

        .qr-bottom-left {
            bottom: 20px;
            left: 20px;
        }

        .qr-bottom-right {
            bottom: 20px;
            right: 20px;
        }

        .decorative-elements {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
        }

        .decorative-cross {
            position: absolute;
            color: rgba(255, 215, 0, 0.1);
            font-size: 3rem;
            font-weight: bold;
        }

        .cross-1 { top: 10%; left: 10%; }
        .cross-2 { top: 15%; right: 15%; }
        .cross-3 { bottom: 20%; left: 20%; }
        .cross-4 { bottom: 10%; right: 10%; }

        @media (max-width: 768px) {
            .welcome-container {
                padding: 30px 20px;
                margin: 10px;
            }
            
            .welcome-title {
                font-size: 2rem;
            }
            
            .user-name {
                font-size: 1.5rem;
            }
            
            .logo {
                width: 80px;
                height: 80px;
            }
            
            .logo::before {
                font-size: 40px;
            }
            
            .info-item {
                flex-direction: column;
                align-items: flex-start;
                gap: 5px;
            }
            
            .qr-codes {
                display: none;
            }
        }

        .success-icon {
            font-size: 4rem;
            color: #27ae60;
            margin-bottom: 20px;
            position: relative;
            z-index: 2;
        }

        .registration-complete {
            background: rgba(39, 174, 96, 0.1);
            color: #27ae60;
            padding: 15px 25px;
            border-radius: 25px;
            font-weight: 600;
            margin-bottom: 20px;
            border: 2px solid rgba(39, 174, 96, 0.3);
            position: relative;
            z-index: 2;
        }
    </style>
</head>
<body>
    <!-- رموز الاستجابة السريعة -->
    <div class="qr-codes">
        <div class="qr-code qr-top-right" title="الصفحة الرئيسية">🏠</div>
        <div class="qr-code qr-bottom-left" title="الصلوات">🙏</div>
        <div class="qr-code qr-bottom-right" title="الإعدادات">⚙️</div>
    </div>

    <div class="welcome-container">
        <!-- عناصر زخرفية -->
        <div class="decorative-elements">
            <div class="decorative-cross cross-1">✞</div>
            <div class="decorative-cross cross-2">✞</div>
            <div class="decorative-cross cross-3">✞</div>
            <div class="decorative-cross cross-4">✞</div>
        </div>

        <div class="success-icon">✅</div>
        
        <div class="registration-complete">
            تم التسجيل بنجاح!
        </div>

        <div class="logo"></div>
        
        <h1 class="welcome-title">مرحباً بك في</h1>
        <h2 class="welcome-title" style="font-size: 1.8rem; margin-bottom: 30px;">برنامج الكنيسة الأجبية والصلوات</h2>
        
        <div class="user-name" id="userName">أهلاً وسهلاً</div>
        <div class="nickname" id="userNickname">نورت البرنامج</div>
        
        <div class="welcome-message">
            نحن سعداء جداً لانضمامك إلى عائلة برنامج الكنيسة. هذا التطبيق سيكون رفيقك في رحلتك الروحية، حيث ستجد الصلوات والتسابيح والأجبية المقدسة.
        </div>

        <div class="verse-container">
            <div class="verse">
                "تعالوا إليّ يا جميع المتعبين والثقيلي الأحمال وأنا أريحكم"
            </div>
            <div class="verse-reference">متى 11:28</div>
        </div>

        <div class="user-info" id="userInfo">
            <!-- سيتم ملؤها بواسطة JavaScript -->
        </div>

        <button class="continue-btn" onclick="startApp()">
            🚀 ابدأ رحلتك الروحية
        </button>
    </div>

    <script src="welcome.js"></script>
</body>
</html>
