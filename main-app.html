<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>برنامج الكنيسة الأجبية والصلوات</title>
    <link href="https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="common-styles.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', 'Amiri', serif;
            font-weight: 700;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 50%, #87ceeb 100%);
            min-height: 100vh;
            color: #333;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .app-container {
            width: 100vw;
            min-height: 100vh;
            position: relative;
            padding: 20px;
            display: flex;
            flex-direction: column;
        }

        .sections-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px 0;
            flex: 1;
        }

        /* خطوط الفصل بين الأقسام */
        .separator-lines {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 5;
            pointer-events: none;
        }

        .separator-line {
            position: absolute;
            top: 50%;
            left: 50%;
            width: 2px;
            height: 50%;
            background: rgba(255, 215, 0, 0.8);
            transform-origin: 50% 100%;
        }



        .app-section {
            background: linear-gradient(145deg, rgba(255, 255, 255, 0.98), rgba(248, 250, 252, 0.95));
            border: 4px solid transparent;
            background-clip: padding-box;
            border-radius: 25px;
            padding: 35px 25px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.4s ease;
            box-shadow:
                0 15px 35px rgba(0, 0, 0, 0.1),
                0 5px 15px rgba(255, 215, 0, 0.2),
                inset 0 1px 0 rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(15px);
            position: relative;
            min-height: 220px;
            overflow: hidden;
        }

        .app-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, #ffd700, #1e3c72, #ffd700, #1e3c72);
            background-size: 400% 400%;
            border-radius: 25px;
            z-index: -1;
            animation: borderGlow 3s ease-in-out infinite;
        }

        @keyframes borderGlow {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        .app-section::after {
            content: '';
            position: absolute;
            top: 4px;
            left: 4px;
            right: 4px;
            bottom: 4px;
            background: inherit;
            border-radius: 21px;
            z-index: -1;
        }

        .app-section:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow:
                0 25px 50px rgba(0, 0, 0, 0.15),
                0 10px 25px rgba(255, 215, 0, 0.4),
                inset 0 1px 0 rgba(255, 255, 255, 1);
        }

        .app-section:hover::before {
            animation-duration: 1s;
        }



        .section-icon {
            font-size: 3.5rem;
            margin-bottom: 15px;
            color: #1e3c72;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
            background: linear-gradient(45deg, #1e3c72, #2a5298);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .app-section:hover .section-icon {
            transform: scale(1.1);
            text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.3);
        }

        .section-title {
            font-size: 1.3rem;
            font-weight: 900;
            color: #1e3c72;
            text-align: center;
            line-height: 1.3;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.15);
            font-family: 'Amiri', serif;
        }

        .section-description {
            font-size: 1rem;
            color: #2a5298;
            text-align: center;
            line-height: 1.4;
            margin-top: 8px;
            font-weight: 700;
        }

        .developer-info {
            position: absolute;
            bottom: 8px;
            left: 8px;
            right: 8px;
            background: rgba(30, 60, 114, 0.1);
            border: 1px solid rgba(30, 60, 114, 0.2);
            border-radius: 8px;
            padding: 6px 10px;
            font-size: 0.65rem;
            color: rgba(30, 60, 114, 0.7);
            font-weight: 900;
            text-align: center;
            backdrop-filter: blur(5px);
            opacity: 0.8;
        }

        .app-watermark {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 0.8rem;
            color: rgba(30, 60, 114, 0.15);
            font-weight: 900;
            text-align: center;
            pointer-events: none;
            z-index: 1;
            line-height: 1.2;
        }

        .app-watermark .logo {
            font-size: 1.5rem;
            margin-bottom: 5px;
            opacity: 0.2;
        }

        .qr-code {
            position: absolute;
            top: 15px;
            right: 15px;
            width: 50px;
            height: 50px;
            background: white;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            z-index: 15;
            border: 2px solid #1e3c72;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .qr-code::before {
            content: '⬜';
            color: #1e3c72;
        }



        .qr-code:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
        }

        .section-badge {
            position: absolute;
            top: 15px;
            left: 15px;
            background: linear-gradient(45deg, #1e3c72, #2a5298);
            color: white;
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            box-shadow: 0 3px 10px rgba(30, 60, 114, 0.3);
            z-index: 10;
        }

        /* مواضع الشرائح على الدائرة - 8 شرائح متساوية */
        .section-1 {
            top: 50%;
            left: 50%;
            transform: translate(-100%, -100%) rotate(0deg);
        }
        .section-1 .section-content {
            transform: rotate(0deg);
        }

        .section-2 {
            top: 50%;
            left: 50%;
            transform: translate(-100%, -100%) rotate(45deg);
        }
        .section-2 .section-content {
            transform: rotate(-45deg);
        }

        .section-3 {
            top: 50%;
            left: 50%;
            transform: translate(-100%, -100%) rotate(90deg);
        }
        .section-3 .section-content {
            transform: rotate(-90deg);
        }

        .section-4 {
            top: 50%;
            left: 50%;
            transform: translate(-100%, -100%) rotate(135deg);
        }
        .section-4 .section-content {
            transform: rotate(-135deg);
        }

        .section-5 {
            top: 50%;
            left: 50%;
            transform: translate(-100%, -100%) rotate(180deg);
        }
        .section-5 .section-content {
            transform: rotate(-180deg);
        }

        .section-6 {
            top: 50%;
            left: 50%;
            transform: translate(-100%, -100%) rotate(225deg);
        }
        .section-6 .section-content {
            transform: rotate(-225deg);
        }

        .section-7 {
            top: 50%;
            left: 50%;
            transform: translate(-100%, -100%) rotate(270deg);
        }
        .section-7 .section-content {
            transform: rotate(-270deg);
        }

        .section-8 {
            top: 50%;
            left: 50%;
            transform: translate(-100%, -100%) rotate(315deg);
        }
        .section-8 .section-content {
            transform: rotate(-315deg);
        }

        .user-info {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(255, 255, 255, 0.9);
            padding: 15px 20px;
            border-radius: 25px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            border: 2px solid rgba(255, 215, 0, 0.3);
        }

        .user-name {
            font-weight: 600;
            color: #1e3c72;
            font-size: 1rem;
        }

        .user-nickname {
            font-size: 0.8rem;
            color: #666;
            margin-top: 2px;
        }

        .app-title {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(255, 255, 255, 0.95);
            padding: 20px 35px;
            border-radius: 25px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            backdrop-filter: blur(15px);
            border: 3px solid rgba(255, 215, 0, 0.4);
            text-align: center;
            max-width: 90%;
        }

        .app-logo {
            font-size: 3rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);
            animation: sparkle 2s ease-in-out infinite;
        }

        @keyframes sparkle {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        .app-title h1 {
            font-size: 1.8rem;
            color: #1e3c72;
            font-family: 'Amiri', serif;
            font-weight: 900;
            margin-bottom: 8px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
        }

        .app-title p {
            font-size: 1rem;
            color: #2a5298;
            font-weight: 700;
            margin-bottom: 5px;
        }

        .app-title .subtitle {
            font-size: 0.9rem;
            color: #666;
            font-weight: 500;
        }

        /* تحسينات الأداء */
        * {
            -webkit-tap-highlight-color: transparent;
            -webkit-touch-callout: none;
            -webkit-user-select: none;
            -khtml-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
        }

        .app-container {
            transform: translateZ(0);
            backface-visibility: hidden;
        }

        /* تأثيرات إضافية */
        .welcome-message {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(255, 255, 255, 0.95);
            padding: 30px;
            border-radius: 20px;
            text-align: center;
            box-shadow: 0 20px 50px rgba(0, 0, 0, 0.2);
            border: 3px solid rgba(255, 215, 0, 0.5);
            z-index: 20;
            opacity: 0;
            visibility: hidden;
            transition: all 0.5s ease;
        }

        .welcome-message.show {
            opacity: 1;
            visibility: visible;
        }

        .close-btn {
            position: absolute;
            top: 10px;
            left: 10px;
            background: #e74c3c;
            color: white;
            border: none;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 1.2rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        @media (max-width: 768px) {
            .circle-container {
                width: 95vmin;
                height: 95vmin;
            }
            
            .app-section {
                width: 100px;
                height: 100px;
            }
            
            .section-icon {
                font-size: 2rem;
            }
            
            .section-title {
                font-size: 0.7rem;
            }
            
            .center-logo {
                width: 80px;
                height: 80px;
            }
            
            .center-logo::before {
                font-size: 40px;
            }
            
            .user-info {
                top: 10px;
                right: 10px;
                padding: 10px 15px;
            }
            
            .app-title {
                bottom: 10px;
                padding: 10px 20px;
            }
            
            .app-title h1 {
                font-size: 1.2rem;
            }
        }
    </style>
</head>
<body>
    <div class="app-container">
        <!-- معلومات المستخدم -->
        <div class="user-info">
            <div class="user-name" id="userName">مرحباً بك</div>
            <div class="user-nickname" id="userNickname">عضو جديد</div>
        </div>

        <!-- عنوان التطبيق -->
        <div class="app-title">
            <div class="app-logo">✨</div>
            <h1>خدمة بلا حدود</h1>
            <p>تطبيق الكنيسة القبطية الأرثوذكسية</p>
            <p class="subtitle">الأجبية والصلوات والسنكسار اليومي</p>
        </div>

        <!-- الأقسام الرئيسية -->
        <div class="sections-container" id="sectionsContainer">

            <!-- القسم الأول: كلمة الله المحيي -->
            <div class="app-section" data-section="word-of-god" data-qr="QR001">
                <div class="section-badge">القسم الأول</div>
                <div class="qr-code" title="رمز الاستجابة السريعة - كلمة الله"></div>
                <div class="app-watermark">
                    <div class="logo">✨</div>
                    <div>خدمة بلا حدود</div>
                </div>
                <div class="section-icon">📖</div>
                <div class="section-title">كلمة الله المحيي</div>
                <div class="section-description">آيات وتأملات من الكتاب المقدس</div>
                <div class="developer-info">الكمبيوتر وتكنولوجيا المعلومات والاتصالات (Bishoy Mourad)</div>
            </div>

            <!-- القسم الثاني: كتاب الـ7 صلوات -->
            <div class="app-section" data-section="seven-prayers" data-qr="QR002">
                <div class="section-badge">القسم الثاني</div>
                <div class="qr-code" title="رمز الاستجابة السريعة - الصلوات السبع"></div>
                <div class="app-watermark">
                    <div class="logo">✨</div>
                    <div>خدمة بلا حدود</div>
                </div>
                <div class="section-icon">🕊️</div>
                <div class="section-title">كتاب الـ7 صلوات</div>
                <div class="section-description">الصلوات السبع المقدسة</div>
                <div class="developer-info">الكمبيوتر وتكنولوجيا المعلومات والاتصالات (Bishoy Mourad)</div>
            </div>

            <!-- القسم الثالث: ألعاب الميديا الحديثة -->
            <div class="app-section" data-section="modern-media" data-qr="QR003">
                <div class="section-badge">القسم الثالث</div>
                <div class="qr-code" title="رمز الاستجابة السريعة - ألعاب الميديا"></div>
                <div class="app-watermark">
                    <div class="logo">✨</div>
                    <div>خدمة بلا حدود</div>
                </div>
                <div class="section-icon">🎯</div>
                <div class="section-title">ألعاب الميديا الحديثة</div>
                <div class="section-description">ألعاب تعليمية وترفيهية</div>
                <div class="developer-info">الكمبيوتر وتكنولوجيا المعلومات والاتصالات (Bishoy Mourad)</div>
            </div>

            <!-- القسم الرابع: أقوال الآباء الأولين -->
            <div class="app-section" data-section="fathers-sayings" data-qr="QR004">
                <div class="section-badge">القسم الرابع</div>
                <div class="qr-code" title="رمز الاستجابة السريعة - أقوال الآباء"></div>
                <div class="app-watermark">
                    <div class="logo">✨</div>
                    <div>خدمة بلا حدود</div>
                </div>
                <div class="section-icon">📜</div>
                <div class="section-title">أقوال الآباء الأولين</div>
                <div class="section-description">حكم وأقوال القديسين</div>
                <div class="developer-info">الكمبيوتر وتكنولوجيا المعلومات والاتصالات (Bishoy Mourad)</div>
            </div>

            <!-- القسم الخامس: كنوز كنيستنا -->
            <div class="app-section" data-section="church-treasures" data-qr="QR005">
                <div class="section-badge">القسم الخامس</div>
                <div class="qr-code" title="رمز الاستجابة السريعة - كنوز الكنيسة"></div>
                <div class="app-watermark">
                    <div class="logo">✨</div>
                    <div>خدمة بلا حدود</div>
                </div>
                <div class="section-icon">⛪</div>
                <div class="section-title">كنوز كنيستنا</div>
                <div class="section-description">تراث الكنيسة العريق</div>
                <div class="developer-info">الكمبيوتر وتكنولوجيا المعلومات والاتصالات (Bishoy Mourad)</div>
            </div>

            <!-- القسم السادس: الصلوات اليومية -->
            <div class="app-section" data-section="daily-prayers" data-qr="QR006">
                <div class="section-badge">القسم السادس</div>
                <div class="qr-code" title="رمز الاستجابة السريعة - الصلوات اليومية"></div>
                <div class="app-watermark">
                    <div class="logo">✨</div>
                    <div>خدمة بلا حدود</div>
                </div>
                <div class="section-icon">🌅</div>
                <div class="section-title">الصلوات اليومية</div>
                <div class="section-description">صلوات الساعات المقدسة</div>
                <div class="developer-info">الكمبيوتر وتكنولوجيا المعلومات والاتصالات (Bishoy Mourad)</div>
            </div>

            <!-- القسم السابع: التسابيح والألحان -->
            <div class="app-section" data-section="hymns" data-qr="QR007">
                <div class="section-badge">القسم السابع</div>
                <div class="qr-code" title="رمز الاستجابة السريعة - التسابيح والألحان"></div>
                <div class="app-watermark">
                    <div class="logo">✨</div>
                    <div>خدمة بلا حدود</div>
                </div>
                <div class="section-icon">🎼</div>
                <div class="section-title">التسابيح والألحان</div>
                <div class="section-description">ألحان الكنيسة الجميلة</div>
                <div class="developer-info">الكمبيوتر وتكنولوجيا المعلومات والاتصالات (Bishoy Mourad)</div>
            </div>

            <!-- القسم الثامن: التقويم الكنسي -->
            <div class="app-section" data-section="calendar" data-qr="QR008">
                <div class="section-badge">القسم الثامن</div>
                <div class="qr-code" title="رمز الاستجابة السريعة - التقويم الكنسي"></div>
                <div class="app-watermark">
                    <div class="logo">✨</div>
                    <div>خدمة بلا حدود</div>
                </div>
                <div class="section-icon">🗓️</div>
                <div class="section-title">التقويم الكنسي</div>
                <div class="section-description">الأعياد والمناسبات</div>
                <div class="developer-info">الكمبيوتر وتكنولوجيا المعلومات والاتصالات (Bishoy Mourad)</div>
            </div>

            <!-- القسم التاسع: السنكسار اليومي -->
            <div class="app-section" data-section="synaxarium" data-qr="QR009">
                <div class="section-badge">القسم التاسع</div>
                <div class="qr-code" title="رمز الاستجابة السريعة - السنكسار اليومي"></div>
                <div class="app-watermark">
                    <div class="logo">✨</div>
                    <div>خدمة بلا حدود</div>
                </div>
                <div class="section-icon">👑</div>
                <div class="section-title">السنكسار اليومي</div>
                <div class="section-description">سيرة القديسين</div>
                <div class="developer-info">الكمبيوتر وتكنولوجيا المعلومات والاتصالات (Bishoy Mourad)</div>
            </div>
        </div>

        <!-- رسالة الترحيب -->
        <div class="welcome-message" id="welcomeMessage">
            <button class="close-btn" onclick="closeWelcome()">×</button>
            <h2 style="color: #1e3c72; margin-bottom: 15px;">مرحباً بك في التطبيق!</h2>
            <p style="color: #666; line-height: 1.6;">
                يمكنك الآن تصفح جميع أقسام التطبيق بسهولة. اضغط على أي قسم للدخول إليه.
                الدائرة ستدور عند تحريك الماوس عليها!
            </p>
        </div>
    </div>

    <script src="main-app.js"></script>
</body>
</html>
