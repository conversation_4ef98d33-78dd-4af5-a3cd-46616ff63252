<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>برنامج الكنيسة الأجبية والصلوات</title>
    <link href="https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="common-styles.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', '<PERSON><PERSON>', serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 50%, #87ceeb 100%);
            min-height: 100vh;
            color: #333;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .app-container {
            width: 100vw;
            min-height: 100vh;
            position: relative;
            padding: 20px;
            display: flex;
            flex-direction: column;
        }

        .sections-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px 0;
            flex: 1;
        }

        /* خطوط الفصل بين الأقسام */
        .separator-lines {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 5;
            pointer-events: none;
        }

        .separator-line {
            position: absolute;
            top: 50%;
            left: 50%;
            width: 2px;
            height: 50%;
            background: rgba(255, 215, 0, 0.8);
            transform-origin: 50% 100%;
        }



        .app-section {
            background: rgba(255, 255, 255, 0.95);
            border: 3px solid rgba(255, 215, 0, 0.8);
            border-radius: 20px;
            padding: 30px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            position: relative;
            min-height: 200px;
        }

        .app-section:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(255, 215, 0, 0.3);
            border-color: rgba(255, 215, 0, 1);
        }



        .section-icon {
            font-size: 2.2rem;
            margin-bottom: 8px;
            color: #1e3c72;
            transform: translateY(-25px);
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
        }

        .section-title {
            font-size: 0.8rem;
            font-weight: 700;
            color: #1e3c72;
            text-align: center;
            line-height: 1.1;
            max-width: 80px;
            transform: translateY(-25px);
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
        }

        .qr-code {
            position: absolute;
            top: 20px;
            right: 20px;
            width: 25px;
            height: 25px;
            background: #ffd700;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.8rem;
            color: #1e3c72;
            box-shadow: 0 3px 10px rgba(255, 215, 0, 0.6);
            z-index: 10;
            border: 2px solid #1e3c72;
            font-weight: bold;
        }

        /* مواضع الشرائح على الدائرة - 8 شرائح متساوية */
        .section-1 {
            top: 50%;
            left: 50%;
            transform: translate(-100%, -100%) rotate(0deg);
        }
        .section-1 .section-content {
            transform: rotate(0deg);
        }

        .section-2 {
            top: 50%;
            left: 50%;
            transform: translate(-100%, -100%) rotate(45deg);
        }
        .section-2 .section-content {
            transform: rotate(-45deg);
        }

        .section-3 {
            top: 50%;
            left: 50%;
            transform: translate(-100%, -100%) rotate(90deg);
        }
        .section-3 .section-content {
            transform: rotate(-90deg);
        }

        .section-4 {
            top: 50%;
            left: 50%;
            transform: translate(-100%, -100%) rotate(135deg);
        }
        .section-4 .section-content {
            transform: rotate(-135deg);
        }

        .section-5 {
            top: 50%;
            left: 50%;
            transform: translate(-100%, -100%) rotate(180deg);
        }
        .section-5 .section-content {
            transform: rotate(-180deg);
        }

        .section-6 {
            top: 50%;
            left: 50%;
            transform: translate(-100%, -100%) rotate(225deg);
        }
        .section-6 .section-content {
            transform: rotate(-225deg);
        }

        .section-7 {
            top: 50%;
            left: 50%;
            transform: translate(-100%, -100%) rotate(270deg);
        }
        .section-7 .section-content {
            transform: rotate(-270deg);
        }

        .section-8 {
            top: 50%;
            left: 50%;
            transform: translate(-100%, -100%) rotate(315deg);
        }
        .section-8 .section-content {
            transform: rotate(-315deg);
        }

        .user-info {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(255, 255, 255, 0.9);
            padding: 15px 20px;
            border-radius: 25px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            border: 2px solid rgba(255, 215, 0, 0.3);
        }

        .user-name {
            font-weight: 600;
            color: #1e3c72;
            font-size: 1rem;
        }

        .user-nickname {
            font-size: 0.8rem;
            color: #666;
            margin-top: 2px;
        }

        .app-title {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(255, 255, 255, 0.9);
            padding: 15px 30px;
            border-radius: 25px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            border: 2px solid rgba(255, 215, 0, 0.3);
            text-align: center;
        }

        .app-title h1 {
            font-size: 1.5rem;
            color: #1e3c72;
            font-family: 'Amiri', serif;
            margin-bottom: 5px;
        }

        .app-title p {
            font-size: 0.9rem;
            color: #666;
        }

        /* تحسينات الأداء */
        * {
            -webkit-tap-highlight-color: transparent;
            -webkit-touch-callout: none;
            -webkit-user-select: none;
            -khtml-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
        }

        .app-container {
            transform: translateZ(0);
            backface-visibility: hidden;
        }

        /* تأثيرات إضافية */
        .welcome-message {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(255, 255, 255, 0.95);
            padding: 30px;
            border-radius: 20px;
            text-align: center;
            box-shadow: 0 20px 50px rgba(0, 0, 0, 0.2);
            border: 3px solid rgba(255, 215, 0, 0.5);
            z-index: 20;
            opacity: 0;
            visibility: hidden;
            transition: all 0.5s ease;
        }

        .welcome-message.show {
            opacity: 1;
            visibility: visible;
        }

        .close-btn {
            position: absolute;
            top: 10px;
            left: 10px;
            background: #e74c3c;
            color: white;
            border: none;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 1.2rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        @media (max-width: 768px) {
            .circle-container {
                width: 95vmin;
                height: 95vmin;
            }
            
            .app-section {
                width: 100px;
                height: 100px;
            }
            
            .section-icon {
                font-size: 2rem;
            }
            
            .section-title {
                font-size: 0.7rem;
            }
            
            .center-logo {
                width: 80px;
                height: 80px;
            }
            
            .center-logo::before {
                font-size: 40px;
            }
            
            .user-info {
                top: 10px;
                right: 10px;
                padding: 10px 15px;
            }
            
            .app-title {
                bottom: 10px;
                padding: 10px 20px;
            }
            
            .app-title h1 {
                font-size: 1.2rem;
            }
        }
    </style>
</head>
<body>
    <div class="app-container">
        <!-- معلومات المستخدم -->
        <div class="user-info">
            <div class="user-name" id="userName">مرحباً بك</div>
            <div class="user-nickname" id="userNickname">عضو جديد</div>
        </div>

        <!-- عنوان التطبيق -->
        <div class="app-title">
            <h1>برنامج الكنيسة الأجبية والصلوات</h1>
            <p>رحلتك الروحية تبدأ من هنا</p>
        </div>

        <!-- الدائرة الرئيسية -->
        <div class="circle-container" id="circleContainer">
            <!-- خطوط الفصل -->
            <div class="separator-lines">
                <div class="separator-line line-1"></div>
                <div class="separator-line line-2"></div>
                <div class="separator-line line-3"></div>
                <div class="separator-line line-4"></div>
                <div class="separator-line line-5"></div>
                <div class="separator-line line-6"></div>
                <div class="separator-line line-7"></div>
                <div class="separator-line line-8"></div>
            </div>

            <!-- الشعار المركزي -->
            <div class="center-logo"></div>

            <!-- الأقسام مع الأسماء والرموز المحدثة -->
            <div class="section-slice section-1">
                <div class="section-content" data-section="word-of-god" data-qr="QR001">
                    <div class="qr-code" title="رمز الاستجابة السريعة - كلمة الله">📱</div>
                    <div class="section-icon">📖</div>
                    <div class="section-title">كلمة الله المحيي</div>
                </div>
            </div>

            <div class="section-slice section-2">
                <div class="section-content" data-section="seven-prayers" data-qr="QR002">
                    <div class="qr-code" title="رمز الاستجابة السريعة - الصلوات السبع">🙏</div>
                    <div class="section-icon">🕊️</div>
                    <div class="section-title">كتاب الـ7 صلوات</div>
                </div>
            </div>

            <div class="section-slice section-3">
                <div class="section-content" data-section="modern-media" data-qr="QR003">
                    <div class="qr-code" title="رمز الاستجابة السريعة - ألعاب الميديا">🎮</div>
                    <div class="section-icon">🎯</div>
                    <div class="section-title">ألعاب الميديا الحديثة</div>
                </div>
            </div>

            <div class="section-slice section-4">
                <div class="section-content" data-section="fathers-sayings" data-qr="QR004">
                    <div class="qr-code" title="رمز الاستجابة السريعة - أقوال الآباء">👴</div>
                    <div class="section-icon">📜</div>
                    <div class="section-title">أقوال الآباء الأولين</div>
                </div>
            </div>

            <div class="section-slice section-5">
                <div class="section-content" data-section="church-treasures" data-qr="QR005">
                    <div class="qr-code" title="رمز الاستجابة السريعة - كنوز الكنيسة">💎</div>
                    <div class="section-icon">⛪</div>
                    <div class="section-title">كنوز كنيستنا</div>
                </div>
            </div>

            <div class="section-slice section-6">
                <div class="section-content" data-section="daily-prayers" data-qr="QR006">
                    <div class="qr-code" title="رمز الاستجابة السريعة - الصلوات اليومية">⏰</div>
                    <div class="section-icon">🌅</div>
                    <div class="section-title">الصلوات اليومية</div>
                </div>
            </div>

            <div class="section-slice section-7">
                <div class="section-content" data-section="hymns" data-qr="QR007">
                    <div class="qr-code" title="رمز الاستجابة السريعة - التسابيح والألحان">🎵</div>
                    <div class="section-icon">🎼</div>
                    <div class="section-title">التسابيح والألحان</div>
                </div>
            </div>

            <div class="section-slice section-8">
                <div class="section-content" data-section="calendar" data-qr="QR008">
                    <div class="qr-code" title="رمز الاستجابة السريعة - التقويم الكنسي">📅</div>
                    <div class="section-icon">🗓️</div>
                    <div class="section-title">التقويم الكنسي</div>
                </div>
            </div>
        </div>

        <!-- رسالة الترحيب -->
        <div class="welcome-message" id="welcomeMessage">
            <button class="close-btn" onclick="closeWelcome()">×</button>
            <h2 style="color: #1e3c72; margin-bottom: 15px;">مرحباً بك في التطبيق!</h2>
            <p style="color: #666; line-height: 1.6;">
                يمكنك الآن تصفح جميع أقسام التطبيق بسهولة. اضغط على أي قسم للدخول إليه.
                الدائرة ستدور عند تحريك الماوس عليها!
            </p>
        </div>
    </div>

    <script src="main-app.js"></script>
</body>
</html>
