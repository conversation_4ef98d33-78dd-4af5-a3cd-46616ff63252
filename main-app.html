<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>برنامج الكنيسة الأجبية والصلوات</title>
    <link href="https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="common-styles.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', '<PERSON><PERSON>', serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 50%, #87ceeb 100%);
            min-height: 100vh;
            color: #333;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .app-container {
            width: 100vw;
            height: 100vh;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .circle-container {
            width: 90vmin;
            height: 90vmin;
            position: relative;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);
            border: 3px solid rgba(255, 215, 0, 0.3);
            backdrop-filter: blur(10px);
            box-shadow: 0 0 50px rgba(255, 215, 0, 0.2);
            overflow: hidden;
        }

        .center-logo {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 120px;
            height: 120px;
            background: linear-gradient(45deg, #ffd700, #ffed4e);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 15px 40px rgba(255, 215, 0, 0.5);
            z-index: 10;
        }

        .center-logo::before {
            content: '✞';
            font-size: 60px;
            color: #1e3c72;
            font-weight: bold;
            text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.3);
        }

        /* شرائح الدائرة */
        .section-slice {
            position: absolute;
            width: 50%;
            height: 50%;
            transform-origin: 100% 100%;
            overflow: hidden;
        }

        .section-content {
            position: absolute;
            width: 200%;
            height: 200%;
            background: rgba(255, 255, 255, 0.9);
            border: 1px solid rgba(255, 215, 0, 0.6);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: background 0.2s ease;
            transform-origin: 50% 100%;
            border-radius: 0 0 100% 0;
        }

        .section-content:hover {
            background: rgba(255, 255, 255, 1);
            border-color: rgba(255, 215, 0, 0.8);
        }

        .section-icon {
            font-size: 1.8rem;
            margin-bottom: 5px;
            color: #1e3c72;
            transform: translateY(-20px);
        }

        .section-title {
            font-size: 0.7rem;
            font-weight: 600;
            color: #1e3c72;
            text-align: center;
            line-height: 1.1;
            max-width: 70px;
            transform: translateY(-20px);
        }

        .qr-code {
            position: absolute;
            top: 15px;
            right: 15px;
            width: 20px;
            height: 20px;
            background: #ffd700;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.6rem;
            color: #1e3c72;
            box-shadow: 0 2px 8px rgba(255, 215, 0, 0.5);
            z-index: 10;
        }

        /* مواضع الشرائح على الدائرة - 8 شرائح متساوية */
        .section-1 {
            top: 50%;
            left: 50%;
            transform: translate(-100%, -100%) rotate(0deg);
        }
        .section-1 .section-content {
            transform: rotate(0deg);
        }

        .section-2 {
            top: 50%;
            left: 50%;
            transform: translate(-100%, -100%) rotate(45deg);
        }
        .section-2 .section-content {
            transform: rotate(-45deg);
        }

        .section-3 {
            top: 50%;
            left: 50%;
            transform: translate(-100%, -100%) rotate(90deg);
        }
        .section-3 .section-content {
            transform: rotate(-90deg);
        }

        .section-4 {
            top: 50%;
            left: 50%;
            transform: translate(-100%, -100%) rotate(135deg);
        }
        .section-4 .section-content {
            transform: rotate(-135deg);
        }

        .section-5 {
            top: 50%;
            left: 50%;
            transform: translate(-100%, -100%) rotate(180deg);
        }
        .section-5 .section-content {
            transform: rotate(-180deg);
        }

        .section-6 {
            top: 50%;
            left: 50%;
            transform: translate(-100%, -100%) rotate(225deg);
        }
        .section-6 .section-content {
            transform: rotate(-225deg);
        }

        .section-7 {
            top: 50%;
            left: 50%;
            transform: translate(-100%, -100%) rotate(270deg);
        }
        .section-7 .section-content {
            transform: rotate(-270deg);
        }

        .section-8 {
            top: 50%;
            left: 50%;
            transform: translate(-100%, -100%) rotate(315deg);
        }
        .section-8 .section-content {
            transform: rotate(-315deg);
        }

        .user-info {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(255, 255, 255, 0.9);
            padding: 15px 20px;
            border-radius: 25px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            border: 2px solid rgba(255, 215, 0, 0.3);
        }

        .user-name {
            font-weight: 600;
            color: #1e3c72;
            font-size: 1rem;
        }

        .user-nickname {
            font-size: 0.8rem;
            color: #666;
            margin-top: 2px;
        }

        .app-title {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(255, 255, 255, 0.9);
            padding: 15px 30px;
            border-radius: 25px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            border: 2px solid rgba(255, 215, 0, 0.3);
            text-align: center;
        }

        .app-title h1 {
            font-size: 1.5rem;
            color: #1e3c72;
            font-family: 'Amiri', serif;
            margin-bottom: 5px;
        }

        .app-title p {
            font-size: 0.9rem;
            color: #666;
        }

        /* تأثير الدوران */
        .rotating {
            animation: rotate 3s ease-in-out;
        }

        @keyframes rotate {
            0% { transform: rotate(0deg); }
            50% { transform: rotate(180deg); }
            100% { transform: rotate(360deg); }
        }

        .section-rotating {
            animation: section-rotate 3s ease-in-out;
        }

        @keyframes section-rotate {
            0% { transform: translate(-50%, -50%) rotate(0deg); }
            50% { transform: translate(-50%, -50%) rotate(-180deg); }
            100% { transform: translate(-50%, -50%) rotate(-360deg); }
        }

        /* تأثيرات إضافية */
        .welcome-message {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(255, 255, 255, 0.95);
            padding: 30px;
            border-radius: 20px;
            text-align: center;
            box-shadow: 0 20px 50px rgba(0, 0, 0, 0.2);
            border: 3px solid rgba(255, 215, 0, 0.5);
            z-index: 20;
            opacity: 0;
            visibility: hidden;
            transition: all 0.5s ease;
        }

        .welcome-message.show {
            opacity: 1;
            visibility: visible;
        }

        .close-btn {
            position: absolute;
            top: 10px;
            left: 10px;
            background: #e74c3c;
            color: white;
            border: none;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 1.2rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        @media (max-width: 768px) {
            .circle-container {
                width: 95vmin;
                height: 95vmin;
            }
            
            .app-section {
                width: 100px;
                height: 100px;
            }
            
            .section-icon {
                font-size: 2rem;
            }
            
            .section-title {
                font-size: 0.7rem;
            }
            
            .center-logo {
                width: 80px;
                height: 80px;
            }
            
            .center-logo::before {
                font-size: 40px;
            }
            
            .user-info {
                top: 10px;
                right: 10px;
                padding: 10px 15px;
            }
            
            .app-title {
                bottom: 10px;
                padding: 10px 20px;
            }
            
            .app-title h1 {
                font-size: 1.2rem;
            }
        }
    </style>
</head>
<body>
    <div class="app-container">
        <!-- معلومات المستخدم -->
        <div class="user-info">
            <div class="user-name" id="userName">مرحباً بك</div>
            <div class="user-nickname" id="userNickname">عضو جديد</div>
        </div>

        <!-- عنوان التطبيق -->
        <div class="app-title">
            <h1>برنامج الكنيسة الأجبية والصلوات</h1>
            <p>رحلتك الروحية تبدأ من هنا</p>
        </div>

        <!-- الدائرة الرئيسية -->
        <div class="circle-container" id="circleContainer">
            <!-- الشعار المركزي -->
            <div class="center-logo"></div>

            <!-- الأقسام -->
            <div class="section-slice section-1">
                <div class="section-content" data-section="word-of-god">
                    <div class="qr-code">📱</div>
                    <div class="section-icon">📖</div>
                    <div class="section-title">كلمة الله المحيي</div>
                </div>
            </div>

            <div class="section-slice section-2">
                <div class="section-content" data-section="seven-prayers">
                    <div class="qr-code">📱</div>
                    <div class="section-icon">🙏</div>
                    <div class="section-title">كتاب الـ7 صلوات</div>
                </div>
            </div>

            <div class="section-slice section-3">
                <div class="section-content" data-section="modern-media">
                    <div class="qr-code">📱</div>
                    <div class="section-icon">🎮</div>
                    <div class="section-title">ألعاب الميديا الحديثة</div>
                </div>
            </div>

            <div class="section-slice section-4">
                <div class="section-content" data-section="fathers-sayings">
                    <div class="qr-code">📱</div>
                    <div class="section-icon">👴</div>
                    <div class="section-title">أقوال الآباء الأولين</div>
                </div>
            </div>

            <div class="section-slice section-5">
                <div class="section-content" data-section="church-treasures">
                    <div class="qr-code">📱</div>
                    <div class="section-icon">💎</div>
                    <div class="section-title">كنوز كنيستنا</div>
                </div>
            </div>

            <div class="section-slice section-6">
                <div class="section-content" data-section="daily-prayers">
                    <div class="qr-code">📱</div>
                    <div class="section-icon">⏰</div>
                    <div class="section-title">الصلوات اليومية</div>
                </div>
            </div>

            <div class="section-slice section-7">
                <div class="section-content" data-section="hymns">
                    <div class="qr-code">📱</div>
                    <div class="section-icon">🎵</div>
                    <div class="section-title">التسابيح والألحان</div>
                </div>
            </div>

            <div class="section-slice section-8">
                <div class="section-content" data-section="calendar">
                    <div class="qr-code">📱</div>
                    <div class="section-icon">📅</div>
                    <div class="section-title">التقويم الكنسي</div>
                </div>
            </div>
        </div>

        <!-- رسالة الترحيب -->
        <div class="welcome-message" id="welcomeMessage">
            <button class="close-btn" onclick="closeWelcome()">×</button>
            <h2 style="color: #1e3c72; margin-bottom: 15px;">مرحباً بك في التطبيق!</h2>
            <p style="color: #666; line-height: 1.6;">
                يمكنك الآن تصفح جميع أقسام التطبيق بسهولة. اضغط على أي قسم للدخول إليه.
                الدائرة ستدور عند تحريك الماوس عليها!
            </p>
        </div>
    </div>

    <script src="main-app.js"></script>
</body>
</html>
