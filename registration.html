<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>التسجيل - برنامج الكنيسة الأجبية والصلوات</title>
    <link href="https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="common-styles.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', '<PERSON><PERSON>', serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 50%, #87ceeb 100%);
            min-height: 100vh;
            color: #333;
            line-height: 1.8;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .registration-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 25px;
            padding: 40px;
            box-shadow: 0 20px 50px rgba(0, 0, 0, 0.15);
            backdrop-filter: blur(10px);
            border: 2px solid rgba(255, 215, 0, 0.3);
            max-width: 500px;
            width: 100%;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
        }

        .logo {
            width: 80px;
            height: 80px;
            margin: 0 auto 20px;
            background: linear-gradient(45deg, #ffd700, #ffed4e);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 8px 25px rgba(255, 215, 0, 0.4);
            position: relative;
        }

        .logo::before {
            content: '✞';
            font-size: 40px;
            color: #1e3c72;
            font-weight: bold;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .app-title {
            font-size: 1.8rem;
            font-weight: 700;
            color: #1e3c72;
            margin-bottom: 10px;
            font-family: 'Amiri', serif;
        }

        .subtitle {
            font-size: 1.1rem;
            color: #666;
            margin-bottom: 20px;
        }

        .form-group {
            margin-bottom: 25px;
        }

        .form-label {
            display: block;
            font-weight: 600;
            color: #1e3c72;
            margin-bottom: 8px;
            font-size: 1rem;
        }

        .form-input {
            width: 100%;
            padding: 15px 20px;
            border: 2px solid rgba(30, 60, 114, 0.2);
            border-radius: 15px;
            font-size: 1rem;
            font-family: 'Cairo', sans-serif;
            background: rgba(255, 255, 255, 0.9);
            transition: all 0.3s ease;
            text-align: right;
        }

        .form-input:focus {
            outline: none;
            border-color: #ffd700;
            box-shadow: 0 0 15px rgba(255, 215, 0, 0.3);
            background: white;
        }

        .gender-options {
            display: flex;
            gap: 20px;
            justify-content: center;
            margin-top: 10px;
        }

        .gender-option {
            display: flex;
            align-items: center;
            gap: 8px;
            cursor: pointer;
            padding: 10px 20px;
            border: 2px solid rgba(30, 60, 114, 0.2);
            border-radius: 25px;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.5);
        }

        .gender-option:hover {
            border-color: #ffd700;
            background: rgba(255, 215, 0, 0.1);
        }

        .gender-option.selected {
            border-color: #1e3c72;
            background: rgba(30, 60, 114, 0.1);
            color: #1e3c72;
            font-weight: 600;
        }

        .gender-option input[type="radio"] {
            display: none;
        }

        .register-btn {
            width: 100%;
            background: linear-gradient(45deg, #1e3c72, #2a5298);
            color: white;
            border: none;
            padding: 18px;
            font-size: 1.2rem;
            font-weight: 600;
            border-radius: 15px;
            cursor: pointer;
            box-shadow: 0 8px 25px rgba(30, 60, 114, 0.3);
            transition: all 0.3s ease;
            font-family: 'Cairo', sans-serif;
            margin-top: 20px;
        }

        .register-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 35px rgba(30, 60, 114, 0.4);
        }

        .register-btn:active {
            transform: translateY(0);
        }

        .progress-bar {
            width: 100%;
            height: 6px;
            background: rgba(30, 60, 114, 0.1);
            border-radius: 3px;
            margin-bottom: 30px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #ffd700, #ffed4e);
            border-radius: 3px;
            width: 0%;
            transition: width 0.5s ease;
        }

        .step-indicator {
            text-align: center;
            margin-bottom: 20px;
            color: #666;
            font-size: 0.9rem;
        }

        .qr-code {
            position: fixed;
            bottom: 20px;
            left: 20px;
            width: 60px;
            height: 60px;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            font-size: 1.5rem;
            color: #1e3c72;
        }

        .error-message {
            color: #e74c3c;
            font-size: 0.9rem;
            margin-top: 5px;
            display: none;
        }

        .success-message {
            color: #27ae60;
            font-size: 0.9rem;
            margin-top: 5px;
            display: none;
        }

        @media (max-width: 768px) {
            .registration-container {
                padding: 25px;
                margin: 10px;
            }
            
            .app-title {
                font-size: 1.5rem;
            }
            
            .gender-options {
                flex-direction: column;
                gap: 10px;
            }
            
            .gender-option {
                justify-content: center;
            }
        }

        /* تأثيرات إضافية */
        .form-group {
            position: relative;
        }

        .form-input:valid {
            border-color: #27ae60;
        }

        .form-input:invalid:not(:placeholder-shown) {
            border-color: #e74c3c;
        }

        .floating-label {
            position: absolute;
            top: 15px;
            right: 20px;
            color: #999;
            transition: all 0.3s ease;
            pointer-events: none;
            background: white;
            padding: 0 5px;
        }

        .form-input:focus + .floating-label,
        .form-input:not(:placeholder-shown) + .floating-label {
            top: -8px;
            font-size: 0.8rem;
            color: #1e3c72;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <!-- رمز الاستجابة السريعة -->
    <div class="qr-code" title="رمز الاستجابة السريعة">
        📱
    </div>

    <div class="registration-container">
        <div class="header">
            <div class="logo"></div>
            <h1 class="app-title">تسجيل عضو جديد</h1>
            <p class="subtitle">انضم إلى عائلة برنامج الكنيسة</p>
        </div>

        <div class="step-indicator">
            الخطوة <span id="currentStep">1</span> من 5
        </div>

        <div class="progress-bar">
            <div class="progress-fill" id="progressFill"></div>
        </div>

        <form id="registrationForm">
            <!-- الخطوة 1: الاسم -->
            <div class="form-step" id="step1">
                <div class="form-group">
                    <label class="form-label">الاسم الكامل *</label>
                    <input type="text" class="form-input" id="fullName" placeholder="أدخل اسمك الكامل" required>
                    <div class="error-message" id="nameError">يرجى إدخال اسم صحيح</div>
                </div>
            </div>

            <!-- الخطوة 2: تاريخ الميلاد -->
            <div class="form-step" id="step2" style="display: none;">
                <div class="form-group">
                    <label class="form-label">تاريخ الميلاد (هجري) *</label>
                    <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 10px;">
                        <input type="number" class="form-input" id="birthDay" placeholder="اليوم" min="1" max="30" required>
                        <select class="form-input" id="birthMonth" required>
                            <option value="">الشهر</option>
                            <option value="1">محرم</option>
                            <option value="2">صفر</option>
                            <option value="3">ربيع الأول</option>
                            <option value="4">ربيع الآخر</option>
                            <option value="5">جمادى الأولى</option>
                            <option value="6">جمادى الآخرة</option>
                            <option value="7">رجب</option>
                            <option value="8">شعبان</option>
                            <option value="9">رمضان</option>
                            <option value="10">شوال</option>
                            <option value="11">ذو القعدة</option>
                            <option value="12">ذو الحجة</option>
                        </select>
                        <input type="number" class="form-input" id="birthYear" placeholder="السنة" min="1300" max="1500" required>
                    </div>
                    <div style="margin-top: 15px; padding: 10px; background: rgba(255, 215, 0, 0.1); border-radius: 10px; text-align: center;">
                        <strong>التاريخ الميلادي المقابل:</strong>
                        <div id="gregorianDate" style="color: #1e3c72; font-weight: 600; margin-top: 5px;">سيظهر بعد إدخال التاريخ الهجري</div>
                    </div>
                    <div class="error-message" id="dateError">يرجى إدخال تاريخ ميلاد صحيح</div>
                </div>
            </div>

            <!-- الخطوة 3: النوع -->
            <div class="form-step" id="step3" style="display: none;">
                <div class="form-group">
                    <label class="form-label">النوع *</label>
                    <div class="gender-options">
                        <label class="gender-option">
                            <input type="radio" name="gender" value="male" required>
                            <span>👨 ذكر</span>
                        </label>
                        <label class="gender-option">
                            <input type="radio" name="gender" value="female" required>
                            <span>👩 أنثى</span>
                        </label>
                    </div>
                    <div class="error-message" id="genderError">يرجى اختيار النوع</div>
                </div>
            </div>

            <!-- الخطوة 4: الصورة (اختيارية) -->
            <div class="form-step" id="step4" style="display: none;">
                <div class="form-group">
                    <label class="form-label">الصورة الشخصية (اختيارية)</label>
                    <input type="file" class="form-input" id="profilePhoto" accept="image/*">
                    <div class="success-message" id="photoSuccess">تم رفع الصورة بنجاح</div>
                </div>
            </div>

            <!-- الخطوة 5: رقم الهاتف -->
            <div class="form-step" id="step5" style="display: none;">
                <div class="form-group">
                    <label class="form-label">رقم الهاتف *</label>
                    <input type="tel" class="form-input" id="phoneNumber" placeholder="01xxxxxxxxx" required>
                    <div class="error-message" id="phoneError">يرجى إدخال رقم هاتف صحيح</div>
                </div>
            </div>

            <button type="button" class="register-btn" id="nextBtn">التالي</button>
        </form>
    </div>

    <script src="registration.js"></script>
</body>
</html>
