/* تأثيرات إضافية وتحسينات بصرية */

/* تأثير الجسيمات المتحركة في الخلفية */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: 
        radial-gradient(circle at 20% 80%, rgba(255, 215, 0, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(30, 60, 114, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(255, 215, 0, 0.05) 0%, transparent 50%);
    animation: float 20s ease-in-out infinite;
    pointer-events: none;
    z-index: -1;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    33% { transform: translateY(-20px) rotate(120deg); }
    66% { transform: translateY(10px) rotate(240deg); }
}

/* تأثير النجوم المتلألئة */
.stars {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: -1;
}

.star {
    position: absolute;
    width: 2px;
    height: 2px;
    background: #ffd700;
    border-radius: 50%;
    animation: twinkle 3s infinite;
}

.star:nth-child(1) { top: 20%; left: 20%; animation-delay: 0s; }
.star:nth-child(2) { top: 40%; left: 80%; animation-delay: 1s; }
.star:nth-child(3) { top: 60%; left: 30%; animation-delay: 2s; }
.star:nth-child(4) { top: 80%; left: 70%; animation-delay: 0.5s; }
.star:nth-child(5) { top: 10%; left: 60%; animation-delay: 1.5s; }

@keyframes twinkle {
    0%, 100% { opacity: 0; transform: scale(1); }
    50% { opacity: 1; transform: scale(1.5); }
}

/* تحسين تأثير الهوفر للعناصر */
.terms-list li {
    position: relative;
    overflow: hidden;
}

.terms-list li::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 215, 0, 0.1), transparent);
    transition: left 0.5s ease;
}

.terms-list li:hover::after {
    left: 100%;
}

/* تأثير النبض للشعار */
@keyframes pulse {
    0% { box-shadow: 0 10px 30px rgba(255, 215, 0, 0.4); }
    50% { box-shadow: 0 15px 40px rgba(255, 215, 0, 0.6); }
    100% { box-shadow: 0 10px 30px rgba(255, 215, 0, 0.4); }
}

.logo {
    animation: pulse 2s ease-in-out infinite;
}

/* تأثير الكتابة المتدرجة */
.app-title {
    background: linear-gradient(45deg, #1e3c72, #ffd700, #1e3c72);
    background-size: 200% 200%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: gradient-shift 3s ease infinite;
}

@keyframes gradient-shift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

/* تأثير الظلال المتحركة */
.header, .content {
    position: relative;
}

.header::before, .content::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: inherit;
    border-radius: inherit;
    filter: blur(20px);
    opacity: 0.3;
    z-index: -1;
    transform: scale(1.05);
}

/* تأثير التمرير السلس */
html {
    scroll-behavior: smooth;
}

/* تأثير الانتقال للعناصر عند التحميل */
.container > * {
    opacity: 0;
    transform: translateY(30px);
    animation: fadeInUp 0.8s ease forwards;
}

.header { animation-delay: 0.2s; }
.content { animation-delay: 0.4s; }
.footer { animation-delay: 0.6s; }

@keyframes fadeInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* تأثير الخط المتموج */
.decorative-border {
    position: relative;
    overflow: hidden;
}

.decorative-border::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: wave 2s linear infinite;
}

@keyframes wave {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* تحسين الاستجابة للشاشات الصغيرة */
@media (max-width: 480px) {
    .logo {
        width: 80px;
        height: 80px;
    }
    
    .logo::before {
        font-size: 40px;
    }
    
    .app-title {
        font-size: 1.5rem;
    }
    
    .section-title {
        font-size: 1.4rem;
    }
    
    .terms-list li {
        padding: 15px;
        margin-bottom: 10px;
    }
    
    .contact-item {
        padding: 10px 15px;
        font-size: 0.9rem;
    }
}

/* تأثير الضوء المتحرك */
.highlight {
    position: relative;
    overflow: hidden;
}

.highlight::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.6), transparent);
    animation: highlight-shine 3s ease-in-out infinite;
}

@keyframes highlight-shine {
    0% { left: -100%; }
    50% { left: 100%; }
    100% { left: -100%; }
}

/* تأثير الحدود المتوهجة */
.content:hover, .header:hover {
    border-color: rgba(255, 215, 0, 0.6);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1), 0 0 20px rgba(255, 215, 0, 0.3);
}

/* تأثير النص المتحرك */
.verse {
    position: relative;
    overflow: hidden;
}

.verse::after {
    content: '✨';
    position: absolute;
    top: 10px;
    right: 10px;
    animation: sparkle 2s ease-in-out infinite;
}

@keyframes sparkle {
    0%, 100% { opacity: 0.5; transform: scale(1) rotate(0deg); }
    50% { opacity: 1; transform: scale(1.2) rotate(180deg); }
}
