/* تحسينات بصرية ثابتة */

/* خلفية ثابتة جميلة */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(circle at 20% 80%, rgba(255, 215, 0, 0.08) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(30, 60, 114, 0.08) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(255, 215, 0, 0.04) 0%, transparent 50%);
    pointer-events: none;
    z-index: -1;
}

/* تحسين تأثير الهوفر للعناصر - ثابت */
.terms-list li {
    position: relative;
    transition: all 0.3s ease;
}

.terms-list li:hover {
    background: rgba(30, 60, 114, 0.12);
    transform: translateX(-3px);
}

/* تأثير التمرير السلس */
html {
    scroll-behavior: smooth;
}

/* ظهور العناصر بشكل ثابت */
.container > * {
    opacity: 1;
    transform: translateY(0);
}

/* تحسين الاستجابة للشاشات الصغيرة */
@media (max-width: 480px) {
    .logo {
        width: 80px;
        height: 80px;
    }
    
    .logo::before {
        font-size: 40px;
    }
    
    .app-title {
        font-size: 1.5rem;
    }
    
    .section-title {
        font-size: 1.4rem;
    }
    
    .terms-list li {
        padding: 15px;
        margin-bottom: 10px;
    }
    
    .contact-item {
        padding: 10px 15px;
        font-size: 0.9rem;
    }
}

/* تأثيرات ثابتة وجميلة */
.highlight {
    position: relative;
}

/* تأثير الحدود عند التمرير - ثابت */
.content:hover, .header:hover {
    border-color: rgba(255, 215, 0, 0.5);
    box-shadow: 0 18px 40px rgba(0, 0, 0, 0.12);
    transition: all 0.3s ease;
}

/* إضافة رمز ثابت للآية */
.verse {
    position: relative;
}

.verse::after {
    content: '✨';
    position: absolute;
    top: 10px;
    right: 10px;
    opacity: 0.7;
}
