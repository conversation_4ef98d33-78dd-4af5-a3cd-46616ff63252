// وظائف بسيطة لصفحة شروط وأحكام برنامج الكنيسة

document.addEventListener('DOMContentLoaded', function() {

    // تأثير بسيط عند النقر على البنود
    const termItems = document.querySelectorAll('.terms-list li');
    termItems.forEach(item => {
        item.addEventListener('click', function() {
            // تأثير بسيط عند النقر
            this.style.transform = 'scale(1.01)';
            this.style.transition = 'transform 0.2s ease';

            setTimeout(() => {
                this.style.transform = 'scale(1)';
            }, 150);
        });
    });

    // تأثير بسيط للشعار عند النقر
    const logo = document.querySelector('.logo');
    if (logo) {
        logo.addEventListener('click', function() {
            this.style.transform = 'scale(0.95)';
            this.style.transition = 'transform 0.1s ease';

            setTimeout(() => {
                this.style.transform = 'scale(1)';
            }, 100);
        });
    }

    // حفظ تفضيلات المستخدم
    function saveUserPreference(key, value) {
        localStorage.setItem(`church_app_${key}`, value);
    }

    function getUserPreference(key, defaultValue) {
        return localStorage.getItem(`church_app_${key}`) || defaultValue;
    }

    // حفظ عدد مرات زيارة الصفحة
    const visitCount = parseInt(getUserPreference('visit_count', '0')) + 1;
    saveUserPreference('visit_count', visitCount.toString());

    console.log(`مرحباً بك! هذه زيارتك رقم ${visitCount} لصفحة الشروط والأحكام`);

    // تحسين إمكانية الوصول
    document.querySelectorAll('.terms-list li').forEach((item, index) => {
        item.setAttribute('tabindex', '0');
        item.setAttribute('role', 'button');
        item.setAttribute('aria-label', `البند رقم ${index + 1} من شروط الاستخدام`);
    });

    // إضافة دعم لوحة المفاتيح
    document.addEventListener('keydown', function(e) {
        if (e.target.classList.contains('terms-list') && e.target.tagName === 'LI') {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                e.target.click();
            }
        }
    });
});
