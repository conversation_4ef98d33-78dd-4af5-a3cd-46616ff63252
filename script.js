// تأثيرات تفاعلية لصفحة شروط وأحكام برنامج الكنيسة

document.addEventListener('DOMContentLoaded', function() {
    
    // تأثير الكتابة التدريجية للعنوان
    function typeWriter(element, text, speed = 100) {
        let i = 0;
        element.innerHTML = '';
        
        function type() {
            if (i < text.length) {
                element.innerHTML += text.charAt(i);
                i++;
                setTimeout(type, speed);
            }
        }
        type();
    }
    
    // تطبيق تأثير الكتابة على العنوان
    const title = document.querySelector('.app-title');
    if (title) {
        const originalText = title.textContent;
        setTimeout(() => {
            typeWriter(title, originalText, 150);
        }, 1000);
    }
    
    // تأثير العد التنازلي للبنود
    const termItems = document.querySelectorAll('.terms-list li');
    termItems.forEach((item, index) => {
        item.style.opacity = '0';
        item.style.transform = 'translateX(50px)';
        
        setTimeout(() => {
            item.style.transition = 'all 0.6s ease';
            item.style.opacity = '1';
            item.style.transform = 'translateX(0)';
        }, (index + 1) * 200);
    });
    
    // تأثير النقر على البنود
    termItems.forEach(item => {
        item.addEventListener('click', function() {
            this.style.transform = 'scale(1.02)';
            this.style.boxShadow = '0 12px 30px rgba(255, 215, 0, 0.3)';
            
            setTimeout(() => {
                this.style.transform = 'scale(1)';
                this.style.boxShadow = '';
            }, 200);
        });
    });
    
    // تأثير الجسيمات المتحركة
    function createParticle() {
        const particle = document.createElement('div');
        particle.style.position = 'fixed';
        particle.style.width = '4px';
        particle.style.height = '4px';
        particle.style.background = 'rgba(255, 215, 0, 0.6)';
        particle.style.borderRadius = '50%';
        particle.style.pointerEvents = 'none';
        particle.style.zIndex = '1000';
        
        const startX = Math.random() * window.innerWidth;
        const startY = window.innerHeight + 10;
        
        particle.style.left = startX + 'px';
        particle.style.top = startY + 'px';
        
        document.body.appendChild(particle);
        
        const duration = Math.random() * 3000 + 2000;
        const endY = -10;
        const endX = startX + (Math.random() - 0.5) * 100;
        
        particle.animate([
            { transform: `translate(0, 0) scale(1)`, opacity: 0 },
            { transform: `translate(${(endX - startX) / 2}px, ${(endY - startY) / 2}px) scale(1.5)`, opacity: 1 },
            { transform: `translate(${endX - startX}px, ${endY - startY}px) scale(0)`, opacity: 0 }
        ], {
            duration: duration,
            easing: 'ease-out'
        }).onfinish = () => {
            particle.remove();
        };
    }
    
    // إنشاء جسيمات بشكل دوري
    setInterval(createParticle, 2000);
    
    // تأثير تتبع الماوس
    let mouseTrail = [];
    const maxTrailLength = 10;
    
    document.addEventListener('mousemove', function(e) {
        mouseTrail.push({ x: e.clientX, y: e.clientY, time: Date.now() });
        
        if (mouseTrail.length > maxTrailLength) {
            mouseTrail.shift();
        }
        
        // إنشاء أثر الماوس
        if (Math.random() < 0.3) {
            const trail = document.createElement('div');
            trail.style.position = 'fixed';
            trail.style.width = '6px';
            trail.style.height = '6px';
            trail.style.background = 'rgba(255, 215, 0, 0.4)';
            trail.style.borderRadius = '50%';
            trail.style.pointerEvents = 'none';
            trail.style.zIndex = '999';
            trail.style.left = (e.clientX - 3) + 'px';
            trail.style.top = (e.clientY - 3) + 'px';
            
            document.body.appendChild(trail);
            
            trail.animate([
                { transform: 'scale(1)', opacity: 0.6 },
                { transform: 'scale(0)', opacity: 0 }
            ], {
                duration: 800,
                easing: 'ease-out'
            }).onfinish = () => {
                trail.remove();
            };
        }
    });
    
    // تأثير النقر على الشعار
    const logo = document.querySelector('.logo');
    if (logo) {
        logo.addEventListener('click', function() {
            // تأثير الانفجار
            for (let i = 0; i < 12; i++) {
                const spark = document.createElement('div');
                spark.style.position = 'fixed';
                spark.style.width = '8px';
                spark.style.height = '8px';
                spark.style.background = '#ffd700';
                spark.style.borderRadius = '50%';
                spark.style.pointerEvents = 'none';
                spark.style.zIndex = '1001';
                
                const rect = logo.getBoundingClientRect();
                const centerX = rect.left + rect.width / 2;
                const centerY = rect.top + rect.height / 2;
                
                spark.style.left = (centerX - 4) + 'px';
                spark.style.top = (centerY - 4) + 'px';
                
                document.body.appendChild(spark);
                
                const angle = (i / 12) * Math.PI * 2;
                const distance = 100;
                const endX = centerX + Math.cos(angle) * distance;
                const endY = centerY + Math.sin(angle) * distance;
                
                spark.animate([
                    { transform: 'translate(0, 0) scale(1)', opacity: 1 },
                    { transform: `translate(${endX - centerX}px, ${endY - centerY}px) scale(0)`, opacity: 0 }
                ], {
                    duration: 1000,
                    easing: 'ease-out'
                }).onfinish = () => {
                    spark.remove();
                };
            }
            
            // تأثير الاهتزاز
            this.style.animation = 'none';
            setTimeout(() => {
                this.style.animation = 'pulse 2s ease-in-out infinite, shake 0.5s ease-in-out';
            }, 10);
        });
    }
    
    // تأثير التمرير السلس للأقسام
    const sections = document.querySelectorAll('.header, .content, .footer');
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.transform = 'translateY(0)';
                entry.target.style.opacity = '1';
            }
        });
    }, observerOptions);
    
    sections.forEach(section => {
        observer.observe(section);
    });
    
    // تأثير الصوت عند النقر (اختياري)
    function playClickSound() {
        // يمكن إضافة ملف صوتي هنا
        const audio = new Audio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT');
        audio.volume = 0.1;
        audio.play().catch(() => {}); // تجاهل الأخطاء
    }
    
    // إضافة الصوت للعناصر التفاعلية
    document.querySelectorAll('.terms-list li, .contact-item, .logo').forEach(element => {
        element.addEventListener('click', playClickSound);
    });
    
    // تأثير الوقت الحقيقي
    function updateTime() {
        const now = new Date();
        const timeString = now.toLocaleTimeString('ar-EG');
        const dateString = now.toLocaleDateString('ar-EG');
        
        // يمكن إضافة عرض الوقت في مكان ما بالصفحة
        console.log(`الوقت الحالي: ${timeString} - التاريخ: ${dateString}`);
    }
    
    setInterval(updateTime, 1000);
    
    // تأثير حفظ تفضيلات المستخدم
    function saveUserPreference(key, value) {
        localStorage.setItem(`church_app_${key}`, value);
    }
    
    function getUserPreference(key, defaultValue) {
        return localStorage.getItem(`church_app_${key}`) || defaultValue;
    }
    
    // حفظ عدد مرات زيارة الصفحة
    const visitCount = parseInt(getUserPreference('visit_count', '0')) + 1;
    saveUserPreference('visit_count', visitCount.toString());
    
    console.log(`مرحباً بك! هذه زيارتك رقم ${visitCount} لصفحة الشروط والأحكام`);
});

// تأثير إضافي للوحة المفاتيح
document.addEventListener('keydown', function(e) {
    // تأثير خاص عند الضغط على مفتاح المسافة
    if (e.code === 'Space') {
        e.preventDefault();
        
        // إنشاء تأثير بصري
        const flash = document.createElement('div');
        flash.style.position = 'fixed';
        flash.style.top = '0';
        flash.style.left = '0';
        flash.style.width = '100%';
        flash.style.height = '100%';
        flash.style.background = 'rgba(255, 215, 0, 0.1)';
        flash.style.pointerEvents = 'none';
        flash.style.zIndex = '9999';
        
        document.body.appendChild(flash);
        
        flash.animate([
            { opacity: 0 },
            { opacity: 1 },
            { opacity: 0 }
        ], {
            duration: 300,
            easing: 'ease-in-out'
        }).onfinish = () => {
            flash.remove();
        };
    }
});

// إضافة CSS للاهتزاز
const shakeCSS = `
@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}
`;

const style = document.createElement('style');
style.textContent = shakeCSS;
document.head.appendChild(style);
